"use client";

import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@geon-ui/react/primitives/accordion";
import { Button } from "@geon-ui/react/primitives/button";
import { Input } from "@geon-ui/react/primitives/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@geon-ui/react/primitives/select";
import * as React from "react";

import { CommVo, StatiscVo } from "../_type/commVo";
import { commUtils } from "../_utils/commUtils";
import StatsDataView from "./statsDataView";
import { useStatsContext } from "./statsProvider";

/**
 * @description 스타일
 */
export default function StatsStyleSetting() {
  const { commVo, commVoClone, commVoApply } = useStatsContext();
  const fontSizeList = Array.from({ length: 21 }, (_, i) =>
    (i + 10).toString(),
  );
  const statsRangeSizeList: string[] = Array.from({ length: 10 }, (_, i) =>
    (i + 1).toString(),
  );

  // 통계 유형에 맞게 급간 개수 조정
  function revalingStatsRange(vo: CommVo) {
    const { statsType, dataList, statsRangeSize } = vo;
    const dataLength = dataList.length;
    if (dataLength === 0) return;
    if (statsType === "Quantile" || statsType === "Jenks") {
      if (dataLength <= Number(statsRangeSize)) {
        vo.statsRangeSize =
          dataLength === 0 ? "1" : (dataLength - 1).toString();
      }
    }
  }

  // 급간 투명도 조절
  function handleColorTrans(e: React.ChangeEvent<HTMLInputElement>) {
    const inputVal = Number(e.target.value);
    const vo = commVoClone();
    vo.statsColorTrans = inputVal;
    commVoApply(vo);
  }

  // 급간 통계 유형 설정
  function handleStatsType(val: string) {
    const inputVal = val as
      | "EqInterval"
      | "Quantile"
      | "StdDeviation"
      | "ArithmeticProgression"
      | "GeometricProgression"
      | "Jenks";
    const vo = commVoClone();
    vo.statsType = inputVal;
    revalingStatsRange(vo);
    commVoApply(vo);
  }

  // 급간 개수 설정
  function handleStatsRangeSize(val: string) {
    const inputVal = val as
      | "1"
      | "2"
      | "3"
      | "4"
      | "5"
      | "6"
      | "7"
      | "8"
      | "9"
      | "10";
    const vo = commVoClone();
    vo.statsRangeSize = inputVal;
    revalingStatsRange(vo);
    commVoApply(vo);
  }

  // 라벨 보이는 축적 설정
  function handleTextLabelLevel(val: string) {
    const inputVal = val as "0" | "200" | "70";
    const vo = commVoClone();
    vo.textLabelLevel = inputVal;
    commVoApply(vo);
  }

  // 폰트 설정
  function handleTextFontWrit(val: string) {
    const inputVal = val as
      | "Dotum"
      | "serif"
      | "cursive"
      | "Monospace"
      | "Fantasy";
    const vo = commVoClone();
    vo.textFontWrit = inputVal;
    commVoApply(vo);
  }

  // 폰트 색상 변경
  function onTextFontColor(e: React.ChangeEvent<HTMLInputElement>) {
    const inputVal = e.target.value;
    const vo = commVoClone();
    vo.textFontColor = inputVal;
    commVoApply(vo);
  }

  // 폰트 크기 변경
  function handleTextFontSize(inputVal: string) {
    const vo = commVoClone();
    vo.textFontSize = inputVal;
    commVoApply(vo);
  }

  // 경계 색상 변경
  function handleOutLineColor(e: React.ChangeEvent<HTMLInputElement>) {
    const inputVal = e.target.value;
    const vo = commVoClone();
    vo.outLineColor = inputVal;
    commVoApply(vo);
  }

  // 경계 선 두께 설정
  function handleOutLineSize(e: React.ChangeEvent<HTMLInputElement>) {
    const inputVal = e.target.value;
    const vo = commVoClone();
    vo.outLineSize = inputVal;
    commVoApply(vo);
  }

  // 경계 선 투명도 설정
  function handleOutLineTrans(e: React.ChangeEvent<HTMLInputElement>) {
    const inputVal = Number(e.target.value);
    const vo = commVoClone();
    vo.outLineTrans = inputVal;
    commVoApply(vo);
  }

  // 라벨 지역명 보이기 설정
  function handleLabelAdm(e: React.ChangeEvent<HTMLInputElement>) {
    const checked = e.target.checked as true | false;
    const vo = commVoClone();
    vo.isLabelAdm = checked ? "Y" : "N";
    commVoApply(vo);
  }

  // 라벨 통계값 보이기 설정
  function handleLabelCal(e: React.ChangeEvent<HTMLInputElement>) {
    const checked = e.target.checked as true | false;
    const vo = commVoClone();
    vo.isLabelCal = checked ? "Y" : "N";
    commVoApply(vo);
  }

  // 급간 색상
  function handleRangColorBox(inputVal: string) {
    const vo = commVoClone();
    vo.statsColor = inputVal;
    vo.statsColorList = [];
    commVoApply(vo);
  }

  // 사용자 지정 색상 초기화
  function handleResetUserColors() {
    const vo = commVoClone();
    vo.dataList.forEach((item: StatiscVo) => {
      item.userClr = null;
    });
    commVoApply(vo);
  }

  return (
    <AccordionItem value="item_step4">
      <AccordionTrigger className="bg-gray-300 px-4 py-2 text-black">
        스타일
      </AccordionTrigger>
      <AccordionContent className="space-y-6 bg-gray-50 p-4 text-sm text-gray-700">
        <div>
          {/* 행정구역 경계 설정 */}
          <div className="mb-2 bg-gray-200 text-sm font-semibold text-gray-700">
            ▣ 행정구역 경계
          </div>
          <div className="mb-2 flex items-center">
            <label
              htmlFor="outLineColor"
              className="w-20 text-sm text-gray-600"
            >
              색상
            </label>

            <input
              id="outLineColor"
              name="outLineColor"
              value={commVo.outLineColor}
              onChange={(e) => handleOutLineColor(e)}
              type="color"
              style={{
                color: "transparent",
                textIndent: "100%",
                whiteSpace: "nowrap",
                overflow: "hidden",
                width: "35px",
                height: "35px",
              }}
            />
            <div className="flex w-20 items-center">
              <Input
                type="text"
                value={commVo.outLineColor}
                placeholder="색상"
                className="flex-1"
                readOnly
              />
            </div>
          </div>

          {/* 선두께 설정 */}
          <div className="mb-2 flex items-center">
            <label htmlFor="outLineSize" className="w-20 text-sm text-gray-600">
              선두께
            </label>
            <div className="flex items-center">
              <div className="w-20">
                <Input
                  type="text"
                  id="outLineSize"
                  name="outLineSize"
                  value={commVo.outLineSize}
                  placeholder="선두께"
                  onChange={(e) => handleOutLineSize(e)}
                />
              </div>
              <span className="mb-2 ml-2 text-gray-500">px</span>
            </div>
          </div>

          {/* 투명도 조절 */}
          <div className="mb-2 flex items-center space-x-4">
            <label
              htmlFor="outLineTrans"
              className="w-20 text-sm text-gray-600"
            >
              투명도
            </label>
            <div className="w-full">
              <Input
                type="range"
                min="1"
                max="100"
                id="outLineTrans"
                name="outLineTrans"
                value={commVo.outLineTrans}
                placeholder="투명도"
                onChange={(e) => handleOutLineTrans(e)}
                className="flex-1"
                style={{ paddingInline: "unset" }}
              />
              <div className="flex justify-between text-xs">
                <span>연하게</span>
                <span>진하게</span>
              </div>
            </div>
          </div>

          {/* 라벨 설정 */}
          <div>
            <div className="mb-3 border-gray-300 bg-gray-200 font-semibold">
              ▣ 라벨 설정
            </div>
            <div className="mb-5 flex items-center gap-2">
              <Input
                type="checkbox"
                id="isLabelAdm"
                name="isLabelAdm"
                value=""
                checked={commVo.isLabelAdm === "Y"}
                onChange={(e) => handleLabelAdm(e)}
                className="h-4 w-4 accent-blue-600"
              />

              <label
                htmlFor="isLabelAdm"
                className="flex items-center gap-2 text-sm text-gray-700"
              >
                지역명
              </label>
              <Input
                type="checkbox"
                id="isLabelCal"
                name="isLabelCal"
                value=""
                checked={commVo.isLabelCal === "Y"}
                onChange={(e) => handleLabelCal(e)}
                className="h-4 w-4 accent-blue-600"
              />
              <label
                htmlFor="isLabelCal"
                className="flex items-center gap-2 text-sm text-gray-700"
              >
                통계값
              </label>
            </div>

            {/* 폰트 설정 */}
            <div className="mb-2 flex items-center gap-2">
              <label
                className="w-20 text-sm text-gray-600"
                htmlFor="textFontWrit"
              >
                폰트
              </label>
              <Select
                onValueChange={handleTextFontWrit}
                value={commVo.textFontWrit}
              >
                <SelectTrigger>
                  <SelectValue placeholder="폰트" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>폰트</SelectLabel>
                    <SelectItem value="Gulim">굴림</SelectItem>
                    <SelectItem value="Dotum">돋움</SelectItem>
                    <SelectItem value="serif">나눔명조</SelectItem>
                    <SelectItem value="cursive">필기체</SelectItem>
                    <SelectItem value="Monospace">모노스페이스(영)</SelectItem>
                    <SelectItem value="Fantasy">판타지(영)</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
              <Select
                onValueChange={handleTextFontSize}
                value={commVo.textFontSize}
              >
                <SelectTrigger className="w-30">
                  <SelectValue placeholder="폰트 사이즈" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>폰트 사이즈</SelectLabel>
                    {fontSizeList.map((size) => (
                      <SelectItem key={size} value={size}>
                        {size}px
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
              <span className="mb-2 text-center">px</span>
            </div>

            {/* 폰트 색상 설정 */}
            <div className="mb-2 flex items-center gap-2">
              <div className="flex items-center space-x-4">
                <label
                  className="w-20 text-sm text-gray-600"
                  htmlFor="textFontColor"
                >
                  폰트색상
                </label>
                <input
                  id="textFontColor"
                  name="textFontColor"
                  value={commVo.textFontColor}
                  onChange={(e) => onTextFontColor(e)}
                  type="color"
                  style={{
                    color: "transparent",
                    textIndent: "100%",
                    whiteSpace: "nowrap",
                    overflow: "hidden",
                    width: "35px",
                    height: "35px",
                  }}
                />
                <Input
                  type="text"
                  value={commVo.textFontColor}
                  placeholder="폰트색상"
                  className="w-37 flex-1"
                  readOnly
                />
              </div>
            </div>

            {/* 라벨 보이기 레벨 설정 */}
            <div className="mb-5 flex gap-2">
              <label
                className="w-20 text-sm text-gray-600"
                htmlFor="textFontWrit"
              >
                라벨 보이기 레벨
              </label>
              <Select
                onValueChange={handleTextLabelLevel}
                value={commVo.textLabelLevel}
              >
                <SelectTrigger>
                  <SelectValue placeholder="전체" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>라벨 보이기 레벨</SelectLabel>
                    <SelectItem value="0">전체</SelectItem>
                    <SelectItem value="200">시군</SelectItem>
                    <SelectItem value="70">읍면동</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>

            {/* 급간 설정 */}
            <div className="mb-3 border-gray-300 bg-gray-200 font-semibold">
              ▣ 급간 설정
            </div>
            <div>
              <div
                className="flex flex-row justify-end"
                onClick={() => handleRangColorBox("red")}
              >
                {commUtils.generateColors("red", 15).map((color, index) => {
                  return (
                    <div
                      key={index}
                      style={{
                        backgroundColor: `${color.hex}`,
                        width: "30px",
                        height: "15px",
                      }}
                    ></div>
                  );
                })}
              </div>
              <div
                className="flex flex-row justify-end"
                onClick={() => handleRangColorBox("brown")}
              >
                {commUtils.generateColors("brown", 15).map((color, index) => {
                  return (
                    <div
                      key={index}
                      style={{
                        backgroundColor: `${color.hex}`,
                        width: "30px",
                        height: "15px",
                      }}
                    ></div>
                  );
                })}
              </div>
              <div
                className="flex flex-row justify-end"
                onClick={() => handleRangColorBox("blue")}
              >
                {commUtils.generateColors("blue", 15).map((color, index) => {
                  return (
                    <div
                      key={index}
                      style={{
                        backgroundColor: `${color.hex}`,
                        width: "30px",
                        height: "15px",
                      }}
                    ></div>
                  );
                })}
              </div>
              <div
                className="flex flex-row justify-end"
                onClick={() => handleRangColorBox("yellow")}
              >
                {commUtils.generateColors("yellow", 15).map((color, index) => {
                  return (
                    <div
                      key={index}
                      style={{
                        backgroundColor: `${color.hex}`,
                        width: "30px",
                        height: "15px",
                      }}
                    ></div>
                  );
                })}
              </div>
              <div
                className="flex flex-row justify-end"
                onClick={() => handleRangColorBox("purple")}
              >
                {commUtils.generateColors("purple", 15).map((color, index) => {
                  return (
                    <div
                      key={index}
                      style={{
                        backgroundColor: `${color.hex}`,
                        width: "30px",
                        height: "15px",
                      }}
                    ></div>
                  );
                })}
              </div>
              <div className="mt-5 flex justify-end">
                <Button
                  onClick={() => handleResetUserColors()}
                  className="w-30 mr-2 h-5 bg-blue-500"
                >
                  사용자컬러 초기화
                </Button>
                <Button
                  onClick={() => handleRangColorBox("random")}
                  className="w-30 h-5 bg-blue-500"
                >
                  랜덤 색상 적용
                </Button>
              </div>
            </div>
            <div className="mb-3 flex items-center space-x-4">
              <label
                htmlFor="statsColorTrans"
                className="w-20 text-sm text-gray-600"
              >
                투명도
              </label>
              <div className="w-full">
                <Input
                  type="range"
                  min="1"
                  max="100"
                  id="statsColorTrans"
                  name="statsColorTrans"
                  value={commVo.statsColorTrans}
                  placeholder="투명도"
                  onChange={(e) => handleColorTrans(e)}
                  className="flex-1"
                  style={{ paddingInline: "unset" }}
                />
                <div className="flex justify-between text-xs">
                  <span>연하게</span>
                  <span>진하게</span>
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <label className="w-20 text-sm text-gray-600">간격</label>
              <Select onValueChange={handleStatsType} value={commVo.statsType}>
                <SelectTrigger>
                  <SelectValue placeholder="간격" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>간격</SelectLabel>
                    <SelectItem value="EqInterval">등가간격</SelectItem>
                    <SelectItem value="Quantile">분위수</SelectItem>
                    <SelectItem value="StdDeviation">표준편차</SelectItem>
                    <SelectItem value="ArithmeticProgression">
                      등차수열
                    </SelectItem>
                    <SelectItem value="GeometricProgression">
                      등비수열
                    </SelectItem>
                    <SelectItem value="Jenks">네추럴 브레이크</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
              <label className="ml-3 mr-3 text-sm text-gray-600">급간</label>
              <Select
                onValueChange={handleStatsRangeSize}
                value={commVo.statsRangeSize}
              >
                <SelectTrigger>
                  <SelectValue placeholder="급간" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>급간</SelectLabel>
                    {statsRangeSizeList.map((size) => (
                      <SelectItem key={size} value={size}>
                        {size}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        <StatsDataView />
      </AccordionContent>
    </AccordionItem>
  );
}
