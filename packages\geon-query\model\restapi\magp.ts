import { ApiResponse } from "utils/fetcher";

import type { API_TYPE } from "../utils/geonAPI";
import { apiHelper } from "../utils/geonAPI";
import {
  AnnounceDetailResponse,
  AttachmentRequest,
  AttachmentResponseResult,
  AuthorListRequest,
  AuthorListResponse,
  ElevationResponse,
  LineStringRequest,
  NoticeCUDRequest,
  NoticeCUDResponse,
  NoticeDeleteRequest,
  NoticeListRequest,
  NoticeListResponse,
  PointStringRequest,
  UserAuthorListRequest,
  UserAuthorListResponse,
  UserAuthorMgmtRequest,
  WaterRequestDTO,
} from "./type/magp-type";

const type: API_TYPE = "magp";

export interface MagpAPIConfig {
  baseUrl?: string;
  crtfckey?: string;
  timeout?: number;
}

export type MagpClient = ReturnType<typeof createGeonMagpClient>;
export function createGeonMagpClient(config: MagpAPIConfig = {}) {
  const { baseUrl, crtfckey } = config;

  const api = apiHelper({ type, baseUrl, crtfckey });

  // TO_DO 삭제
  // API 배포 미반영 혹은 로컬 개발 중 사용
  const apiLocal = apiHelper({
    type,
    baseUrl: "http://localhost:10170/api/",
    crtfckey,
  });

  return {
    table: {
      table: api.get<{ schema: string; table: string }>("/table"),
    },
    water: {
      //인터페이스(view) 조회
      list: api.post<WaterRequestDTO>("/water/list"),
      //유량계
      flowSelect: api.get<{ gid: string }>("/water/flow/select"),
      //스탠드파이프
      stpiSelect: api.get<{ gid: string }>("/water/stpi/select"),
      //수압계
      prgaSelect: api.get<{ gid: string }>("/water/prga/select"),
      //소방시설
      fireSelect: api.get<{ gid: string }>("/water/fire/select"),
      //상수맨홀
      manhSelect: api.get<{ gid: string }>("/water/manh/select"),
      //상수관로
      pipeSelect: api.get<{ gid: string }>("/water/pipe/select"),
      //급수관로
      splySelect: api.get<{ gid: string }>("/water/sply/select"),
    },
    elevation: {
      point: api.post<PointStringRequest, ElevationResponse>(
        "/elevation/point",
      ),
      line: api.post<LineStringRequest, ElevationResponse>("/elevation/line"),
    },
    notice: {
      list: api.get<NoticeListRequest, NoticeListResponse>("/notice/list"),
      select: api.get<{ nttId: string }, AnnounceDetailResponse>(
        "/notice/select",
      ),
      delete: api.post<NoticeDeleteRequest, NoticeCUDResponse>(
        "/notice/delete",
      ),
      insert: api.post<NoticeCUDRequest, NoticeCUDResponse>("/notice/insert"),
      update: api.post<NoticeCUDRequest, NoticeCUDResponse>("/notice/update"),
    },
    dataspace: {
      list: api.get<NoticeListRequest, NoticeListResponse>("/dataspace/list"),
      select: api.get<{ nttId: string }, AnnounceDetailResponse>(
        "/dataspace/select",
      ),
      delete: api.post<NoticeDeleteRequest, NoticeCUDResponse>(
        "/dataspace/delete",
      ),
      insert: api.post<NoticeCUDRequest, NoticeCUDResponse>(
        "/dataspace/insert",
      ),
      update: api.post<NoticeCUDRequest, NoticeCUDResponse>(
        "/dataspace/update",
      ),
    },
    attachment: {
      list: api.get<AttachmentRequest, AttachmentResponseResult>(
        "/attachment/list",
      ),
      download: api.post<AttachmentRequest>("/attachment/download"),
      upload: api.upload("/attachment/upload"),
      delete: api.get<AttachmentRequest>("/attachment/delete"),
    },
    author: {
      // API 배포 반영 될 시,
      itemList: api.get<AuthorListRequest, AuthorListResponse>(
        "/admin/author/item/list",
      ),
      // 사용자 정보와 사용자 권한정보 목록
      list: api.get<UserAuthorListRequest, UserAuthorListResponse>(
        "/admin/author/list",
      ),
      // 사용자 권한정보 등록
      insert: api.post<UserAuthorMgmtRequest, ApiResponse<number>>(
        "/admin/author/insert",
      ),
      // 사용자 권한정보 삭제
      delete: api.post<UserAuthorMgmtRequest, ApiResponse<number>>(
        "/admin/author/delete",
      ),
      update: api.post<UserAuthorMgmtRequest, ApiResponse<number>>(
        "/admin/author/merge",
      ),

      // API 배포 미반영 혹은 로컬 개발 중
      // 시스템 내의 권한 목록 조회
      /*
      itemList: apiLocal.get<AuthorListRequest, AuthorListResponse>(
        "/admin/author/item/list",
      ),
      // 사용자 정보와 사용자 권한정보 목록
      list: apiLocal.get<UserAuthorListRequest, UserAuthorListResponse>(
        "/admin/author/list",
      ),
      // 사용자 권한정보 등록
      insert: apiLocal.post<UserAuthorMgmtRequest, ApiResponse<number>>(
        "/admin/author/insert",
      ),
      // 사용자 권한정보 삭제
      delete: apiLocal.post<UserAuthorMgmtRequest, ApiResponse<number>>(
        "/admin/author/delete",
      ),
      update: apiLocal.post<UserAuthorMgmtRequest, ApiResponse<number>>(
        "/admin/author/merge",
      ),
      */
    },
  };
}
