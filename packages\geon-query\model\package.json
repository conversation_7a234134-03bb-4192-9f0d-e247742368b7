{"name": "@geon-query/model", "version": "0.0.0", "private": true, "type": "module", "exports": {".": "./index.ts", "./domain/*": "./domain/*.ts", "./utils/*": "./utils/*.ts", "./restapi/*": "./restapi/*.ts"}, "scripts": {"check-types": "tsc --noEmit"}, "dependencies": {"@tanstack/react-query": "^5.89.0", "react": "^19.1.1", "react-dom": "^19.1.1"}, "devDependencies": {"@config/eslint": "workspace:*", "@config/typescript": "workspace:*", "eslint": "^9.35.0"}}