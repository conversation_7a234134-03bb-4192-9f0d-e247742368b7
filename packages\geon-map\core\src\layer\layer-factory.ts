import type { BasemapInfo, ODF, ODF_MAP } from "../types";
import {
  execute,
  LayerLike,
  QueryOptions,
  setting,
} from "../utils/spatialQuery";
import { BaseLayer, GeoJsonLayer, GeoserverLayer } from "./implementations";
import {
  AbstractLayer,
  GeoJsonLayerConfig,
  GeoserverLayerConfig,
  LayerConfig,
  LayerCreationResult,
  LayerType,
} from "./layer-types";

/**
 * 전략 패턴을 활용한 레이어 팩토리 클래스
 * ODF 의존성을 격리하고 추상화된 Layer 객체를 생성합니다.
 */
const BASEMAP_WIDGET_ID = "odf-basemap-widget";

export class LayerFactory {
  private map: ODF_MAP;
  private odf: ODF;
  private static duplicateLayerCache = new Map<string, string>(); // layerKey -> layerId

  constructor(map: ODF_MAP, odf: ODF) {
    this.map = map;
    this.odf = odf;
    setting(this.map, this.odf);
  }

  spatialQuery(layer: LayerLike, options: QueryOptions) {
    return execute(layer, options);
  }

  /**
   * 추상화된 레이어 생성 (메인 메서드)
   * LayerConfig 또는 기존 LayerProps 모두 지원
   */
  async createLayer(input: LayerConfig | any): Promise<LayerCreationResult> {
    // 입력 형태 감지 및 표준화
    const config = this.normalizeLayerInput(input);
    try {
      // 중복 체크
      const duplicateResult = this.checkDuplicateLayer(config);
      if (duplicateResult.isDuplicate) {
        return {
          success: true,
          layer: duplicateResult.layer!,
          type: "duplicate",
          duplicateLayerId: duplicateResult.layer!.getId(),
        };
      }

      // ODF 파라미터로 변환
      const odfParams = this.convertToODFParams(config);

      // ODF 레이어 생성
      const odfLayer = this.odf.LayerFactory.produce(
        odfParams.type,
        odfParams.params,
      );

      if (!odfLayer) {
        throw new Error(`Failed to create ODF layer of type: ${config.type}`);
      }

      // 지도에 추가 및 초기 설정
      await this.setupODFLayer(odfLayer, config);

      // 추상화된 레이어 래퍼 생성
      const abstractLayer = this.createLayerWrapper(
        config.type,
        odfLayer,
        config,
      );

      // 캐시에 등록
      const layerKey = this.generateLayerKey(config);
      LayerFactory.duplicateLayerCache.set(layerKey, abstractLayer.getId());

      return {
        success: true,
        layer: abstractLayer,
        type: "created",
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error : new Error(String(error)),
        type: "failed",
      };
    }
  }

  /**
   * 레이어 중복 체크
   */
  private checkDuplicateLayer(config: LayerConfig): {
    isDuplicate: boolean;
    layer?: AbstractLayer;
  } {
    const layerKey = this.generateLayerKey(config);
    const cachedLayerId = LayerFactory.duplicateLayerCache.get(layerKey);

    if (cachedLayerId) {
      // 캐시된 레이어가 실제로 맵에 존재하는지 확인
      const existingODFLayers = this.map.getODFLayers();
      const existingODFLayer = existingODFLayers.find(
        (layer: any) => layer.getODFId() === cachedLayerId,
      );

      if (existingODFLayer) {
        // 기존 레이어로 추상화된 래퍼 생성
        const existingLayer = this.createLayerWrapper(
          config.type,
          existingODFLayer,
          config,
        );
        return {
          isDuplicate: true,
          layer: existingLayer,
        };
      } else {
        // 캐시 정리
        LayerFactory.duplicateLayerCache.delete(layerKey);
      }
    }

    return { isDuplicate: false };
  }

  /**
   * 레이어 식별용 키 생성
   */
  private generateLayerKey(config: LayerConfig): string {
    switch (config.type) {
      case "geoserver":
        const geoConfig = config as GeoserverLayerConfig;
        return `geoserver:${geoConfig.layer}`;
      default:
        return `${config.type}:${config.id || "default"}`;
    }
  }

  /**
   * 전략 패턴: 레이어 타입별 래퍼 생성
   */
  private createLayerWrapper(
    type: LayerType,
    odfLayer: any,
    config: LayerConfig,
  ): AbstractLayer {
    switch (type) {
      case "geoserver":
        return new GeoserverLayer(odfLayer, config as GeoserverLayerConfig);
      case "geojson":
        return new GeoJsonLayer(odfLayer, config as GeoJsonLayerConfig);
      case "kml":
      case "csv":
      case "geotiff":
      case "api":
      case "svg":
      case "empty":
        return new BaseLayer(odfLayer, config);
      default:
        throw new Error(`Unsupported layer type: ${type}`);
    }
  }

  /**
   * ODF 레이어 초기 설정
   */
  private async setupODFLayer(
    odfLayer: any,
    config: LayerConfig,
  ): Promise<void> {
    // 지도에 추가
    odfLayer.setMap(this.map);

    // 스타일 적용
    if (config.renderOptions?.style) {
      this.applyLayerStyle(odfLayer, config.renderOptions.style, config.type);
    }

    // 🚫 초기 속성 적용은 react-odf에서 담당
    // - visible, opacity, zIndex 등은 컴포넌트에서 설정
    // - autoFit도 컴포넌트에서 제어
  }

  /**
   * 스타일 적용 헬퍼
   */
  private applyLayerStyle(
    odfLayer: any,
    style: any,
    layerType: LayerType,
  ): void {
    try {
      const parsedStyle = typeof style === "string" ? JSON.parse(style) : style;

      if (layerType === "geoserver") {
        // Geoserver WMS는 SLD 스타일 사용
        const sldStyle = this.odf.StyleFactory?.produceSLD?.(parsedStyle);
        if (sldStyle && odfLayer.setSLD) {
          odfLayer.setSLD(sldStyle);
        }
      } else {
        // 기타는 일반 스타일 사용
        const odfStyle = this.odf.StyleFactory?.produce?.(parsedStyle);
        if (odfStyle && odfLayer.setStyle) {
          odfLayer.setStyle(odfStyle);
        }
      }
    } catch (error) {
      console.error(
        `[LayerFactory] Failed to apply style for ${layerType}:`,
        error,
      );
    }
  }

  /**
   * 레이어 제거
   */
  removeLayer(layerId: string): boolean {
    try {
      this.map.removeLayer(layerId);

      // 캐시에서도 제거
      for (const [
        key,
        cachedId,
      ] of LayerFactory.duplicateLayerCache.entries()) {
        if (cachedId === layerId) {
          LayerFactory.duplicateLayerCache.delete(key);
          break;
        }
      }

      return true;
    } catch (error) {
      console.error(`[LayerFactory] Failed to remove layer ${layerId}:`, error);
      return false;
    }
  }

  /**
   * 입력을 표준 LayerConfig로 변환 (다양한 입력 형태 지원)
   */
  private normalizeLayerInput(input: any): LayerConfig {
    // 이미 LayerConfig 형태인 경우
    if (input.type && typeof input === "object" && !input.layer) {
      return input as LayerConfig;
    }

    // 기존 LayerProps 형태인 경우 변환
    if (input.type === "geoserver" && input.layer) {
      return {
        type: "geoserver",
        id: input.id,
        name: input.name,
        visible: input.visible,
        opacity: input.opacity,
        zIndex: input.zIndex,
        autoFit: input.fit,
        renderOptions: input.renderOptions,
        // Geoserver 특화 필드들
        layer: input.layer,
        server: input.server,
        service: input.service,
        method: input.method,
        bbox: input.bbox,
        crtfckey: input.crtfckey,
        projection: input.projection,
        limit: input.limit,
        tiled: input.tiled,
        geometryType: input.geometryType,
        serviceTy: input.serviceTy,
        cqlFilter: input.cqlFilter,
        info: input.info,
      } as GeoserverLayerConfig;
    }

    // 기타 레이어 타입들도 필요에 따라 추가
    return {
      type: input.type,
      id: input.id,
      name: input.name,
      visible: input.visible,
      opacity: input.opacity,
      zIndex: input.zIndex,
      autoFit: input.fit,
      renderOptions: input.renderOptions,
      ...input, // 나머지 필드들 그대로 복사
    } as LayerConfig;
  }

  /**
   * 레이어 설정을 ODF 파라미터로 변환
   */
  private convertToODFParams(config: LayerConfig): {
    type: LayerType;
    params: any;
  } {
    const { type, renderOptions } = config;

    switch (type) {
      case "geoserver": {
        const geoserverConfig = config as GeoserverLayerConfig;
        return {
          type,
          params: {
            server:
              typeof geoserverConfig.server === "string"
                ? geoserverConfig.server
                : geoserverConfig.server.url,
            layer: geoserverConfig.layer,
            service: geoserverConfig.service,
            method: geoserverConfig.method || "get",
            bbox: geoserverConfig.bbox || false,
            crtfckey: geoserverConfig.crtfckey || "",
            projection: geoserverConfig.projection || "EPSG:5186",
            limit: geoserverConfig.limit,
            tiled: geoserverConfig.tiled,
            geometryType: geoserverConfig.geometryType,
            serviceTy: geoserverConfig.serviceTy,
            ...(typeof geoserverConfig.server !== "string" && {
              version: geoserverConfig.server.version,
              proxyURL: geoserverConfig.server.proxyURL,
              proxyParam: geoserverConfig.server.proxyParam,
            }),
          },
        };
      }
      case "geojson": {
        const geojsonConfig = config as GeoJsonLayerConfig;
        return {
          type,
          params: {
            data: geojsonConfig.data,
            dataProjectionCode: geojsonConfig.dataProjectionCode,
            featureProjectionCode: geojsonConfig.featureProjectionCode,
            service: geojsonConfig.service,
            renderOptions,
          },
        };
      }
      default:
        // 기타 레이어 타입들은 기본 파라미터 사용
        return {
          type,
          params: {
            renderOptions,
            // 추가 파라미터들은 config에서 직접 추출
            ...(config as any),
          },
        };
    }
  }

  /**
   * 기존 produce 메서드 호환성 유지 (deprecated)
   * @deprecated Use createLayer instead
   */
  produce(type: any, params: any): any {
    console.warn(
      "[LayerFactory] produce() is deprecated. Use createLayer() instead.",
    );
    return this.odf.LayerFactory.produce(type, params);
  }

  setBasemapInfo(basemapInfo: BasemapInfo) {
    try {
      //TODO 상태관리 및 기본 베이스맵 레이어 삭제 필요.
      this.map.removeLayer(BASEMAP_WIDGET_ID);
      const apiParameter = {
        ...JSON.parse(basemapInfo.mapUrlparamtr),
        service: basemapInfo.lyrStleCodeNm?.toLocaleLowerCase(),
        server: basemapInfo.mapUrl,
      };

      const basemapLayer = this.odf.LayerFactory.produce("api", apiParameter);
      basemapLayer.setODFId(BASEMAP_WIDGET_ID);
      basemapLayer.setMap(this.map);
      return basemapLayer;
    } catch (e) {
      console.error(e);
    }
  }
  /**
   * 레이어 옵션을 ODF 파라미터로 변환
   */
  static convertToODFParams(options: LayerOptions): {
    type: LayerType;
    params: any;
  } {
    const { type, renderOptions } = options;

    switch (type) {
      case "geoserver": {
        const geoserverOptions = options as GeoserverLayerOptions;
        return {
          type,
          params: {
            server:
              typeof geoserverOptions.server === "string"
                ? geoserverOptions.server
                : geoserverOptions.server.url,
            layer: geoserverOptions.layer,
            service: geoserverOptions.service,
            method: geoserverOptions.method || "get",
            bbox: geoserverOptions.bbox || false,
            crtfckey: geoserverOptions.crtfckey || "",
            projection: geoserverOptions.projection || "EPSG:5186",
            limit: geoserverOptions.limit,
            tiled: geoserverOptions.tiled,
            geometryType: geoserverOptions.geometryType,
            serviceTy: geoserverOptions.serviceTy,
            ...(typeof geoserverOptions.server !== "string" && {
              version: geoserverOptions.server.version,
              proxyURL: geoserverOptions.server.proxyURL,
              proxyParam: geoserverOptions.server.proxyParam,
            }),
          },
        };
      }

      case "geotiff": {
        const geotiffOptions = options as any;
        return {
          type,
          params: {
            sources: geotiffOptions.sources,
            normalize: geotiffOptions.normalize ?? true,
            wrapX: geotiffOptions.wrapX,
            opaque: geotiffOptions.opaque,
            transition: geotiffOptions.transition,
            renderOptions,
          },
        };
      }

      case "geojson": {
        const geojsonOptions = options as any;
        return {
          type,
          params: {
            data: geojsonOptions.data,
            dataProjectionCode: geojsonOptions.dataProjectionCode,
            featureProjectionCode: geojsonOptions.featureProjectionCode,
            service: geojsonOptions.service,
            renderOptions,
          },
        };
      }

      case "kml": {
        const kmlOptions = options as any;
        return {
          type,
          params: {
            data: kmlOptions.data,
            dataProjectionCode: kmlOptions.dataProjectionCode,
            featureProjectionCode: kmlOptions.featureProjectionCode,
            renderOptions,
          },
        };
      }

      case "csv": {
        const csvOptions = options as any;
        return {
          type,
          params: {
            data: csvOptions.data,
            dataProjectionCode: csvOptions.dataProjectionCode,
            featureProjectionCode: csvOptions.featureProjectionCode,
            geometryColumnName: csvOptions.geometryColumnName,
            delimiter: csvOptions.delimiter,
            renderOptions,
          },
        };
      }

      case "api": {
        const apiOptions = options as any;
        return {
          type,
          params: {
            server:
              typeof apiOptions.server === "string"
                ? apiOptions.server
                : apiOptions.server.url,
            service: apiOptions.service,
            bbox: apiOptions.bbox,
            tiled: apiOptions.tiled,
            tileGrid: apiOptions.tileGrid,
            originalOption: apiOptions.originalOption,
            parameterFilter: apiOptions.parameterFilter,
            ...(typeof apiOptions.server !== "string" && {
              proxyURL: apiOptions.server.proxyURL,
              proxyParam: apiOptions.server.proxyParam,
            }),
            renderOptions,
          },
        };
      }

      case "svg": {
        const svgOptions = options as any;
        return {
          type,
          params: {
            svgContainer: svgOptions.svgContainer,
            extent: svgOptions.extent,
            renderOptions,
          },
        };
      }

      default:
        throw new Error(`Unsupported layer type: ${type}`);
    }
  }

  /**
   * ODF 레이어 인스턴스 생성
   */
  static createODFLayer(type: LayerType, params: any): any {
    if (typeof (globalThis as any).odf === "undefined") {
      throw new Error("ODF library is not loaded");
    }

    return (globalThis as any).odf.LayerFactory.produce(type, params);
  }

  // ===== 레이어 관리 헬퍼 메서드 (최소한의 유틸리티만) =====

  /**
   * 레이어 제거 (캐시 관리용)
   */
  removeLayerFromCache(layerId: string): void {
    // 캐시에서만 제거
    for (const [key, cachedId] of LayerFactory.duplicateLayerCache.entries()) {
      if (cachedId === layerId) {
        LayerFactory.duplicateLayerCache.delete(key);
        break;
      }
    }
  }

  /**
   * CQL 필터 설정 헬퍼 (ODF 글로벌 메서드 래핑)
   */
  static setLayerFilter(odfLayer: any, cqlFilter: string): boolean {
    try {
      if (
        typeof (globalThis as any).odf?.Layer?.setLayerFilter === "function"
      ) {
        (globalThis as any).odf.Layer.setLayerFilter(odfLayer, cqlFilter);
        return true;
      }
      return false;
    } catch (error) {
      console.error(`[LayerFactory] Failed to set filter:`, error);
      return false;
    }
  }
}
