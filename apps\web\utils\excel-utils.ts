import * as XLSX from "xlsx";

export type ExcelColumn = {
  columnName: string;
  columnType: string;
  columnComment: string;
  ordinalPosition: number;
};

// 엑셀 다운로드
export function downloadToExcel(
  columns: ExcelColumn[],
  rows: Record<string, unknown>[],
  fileName: string,
) {
  const toCamelCase = (snake: string) => {
    return snake.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  };

  const header = columns.map((col) => col.columnComment);
  const body = rows.map((row) =>
    columns.map((col) => row[toCamelCase(col.columnName)] ?? ""),
  );

  const worksheet = XLSX.utils.aoa_to_sheet([header, ...body]);
  const workbook = XLSX.utils.book_new();

  XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

  const excelBuffer = XLSX.write(workbook, { bookType: "xlsx", type: "array" });
  const blob = new Blob([excelBuffer], { type: "application/octet-stream" });

  const url = window.URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  // 파일 이름
  a.download = `${fileName}.xlsx`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  window.URL.revokeObjectURL(url);
}
