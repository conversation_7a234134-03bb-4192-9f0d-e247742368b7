"use client";

import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@geon-ui/react/primitives/accordion";
import { Input } from "@geon-ui/react/primitives/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@geon-ui/react/primitives/select";
import * as React from "react";

import { CalFormula, CommVo, StatiscVo, YesNo } from "../_type/commVo";
import { PresetDataVo, PresetVo } from "../_type/presetVo";
import StatsDataView from "./statsDataView";
import { useStatsContext } from "./statsProvider";

/**
 * @description 보조통계
 */
export default function StatsPresetSetting() {
  const { commVo, setCommVo, commVoClone, commVoApply } = useStatsContext();

  // 통계 데이터의 보조통계값을 0으로 초기화하는 함수
  function resetDataList(vo: CommVo) {
    vo.dataList.forEach((item: StatiscVo) => {
      item.subVal = 0;
      item.calVal = 0;
    });
  }

  // 선택된 프리셋에 맞춰 보조통계값을 초기화하는 함수
  function PresetDataToDataList(vo: CommVo) {
    vo.dataList.forEach((item: StatiscVo) => {
      const PresetItem = vo.subPresetDataList.filter(
        (subItem: PresetDataVo) => {
          if (subItem.code === item.code) return subItem;
        },
      );
      item.subVal = PresetItem.length > 0 ? Number(PresetItem[0]?.val) : 0;
    });
  }

  // 보조통계 사용 여부 설정 (Y: 사용, N: 미사용)
  function handleSubValYn(e: React.ChangeEvent<HTMLInputElement>) {
    const inputVal = e.target.value as YesNo;
    const vo = commVoClone();
    vo.subValYn = inputVal || "Y";
    if (inputVal === "N") {
      vo.subPresetVal = "";
      vo.subPresetCalUnit = "1";
      vo.calFormula = "T0";
      vo.stsUnit = "";
      resetDataList(vo);
    }
    commVoApply(vo);
  }

  // 프리셋 사용 여부 설정
  function handleSubPresetYn(e: React.ChangeEvent<HTMLInputElement>) {
    const inputVal = e.target.value as YesNo;
    const vo = commVoClone();
    vo.subPresetVal = "";
    vo.subPresetCalUnit = "1";
    vo.calFormula = "T1";
    vo.stsUnit = "";
    vo.subPresetYn = inputVal || "Y";
    resetDataList(vo);
    commVoApply(vo);
  }

  // 직접 입력을 통한 통계 단위 설정
  function handleInputStsUnit(e: React.ChangeEvent<HTMLInputElement>) {
    const inputVal = e.target.value;
    const vo = commVoClone();
    vo.stsUnit = inputVal;
    setCommVo(vo);
  }

  // 프리셋 목록을 선택할 때 호출되는 함수
  function handleSubPresetList(val: string) {
    const vo = commVoClone();
    vo.subPresetVal = val || "";
    PresetDataToDataList(vo);
    commVoApply(vo);
  }

  // 단위 조정 함수 (1, 1000, 10000 등으로 설정)
  function handleSubPresetCalUnit(val: string) {
    const inputVal = val as "1" | "1000" | "10000" | "100000" | "1000000";
    const vo = commVoClone();
    vo.subPresetCalUnit = inputVal || "1";
    PresetDataToDataList(vo);
    commVoApply(vo);
  }

  // 계산식 선택 (T0: 미적용, T1: 나누기, T2: 비율, T3: 비중)
  function handleCalFormula(e: React.ChangeEvent<HTMLInputElement>) {
    const inputVal = e.target.value as CalFormula;
    const vo = commVoClone();
    vo.calFormula = inputVal || "T0";
    commVoApply(vo);
  }

  return (
    <AccordionItem value="item_step3">
      <AccordionTrigger className="bg-gray-300 px-4 py-2 text-black">
        보조통계
      </AccordionTrigger>

      <AccordionContent className="space-y-6 bg-gray-50 p-4 text-sm text-gray-700">
        <div>
          {/* 보조통계 사용 여부 */}
          <div>
            <div className="mb-2 bg-gray-200 font-semibold">
              ▣ 보조통계 사용여부
            </div>
            <div className="flex items-center gap-2">
              <Input
                type="radio"
                id="subValY"
                name="subValYn"
                value="Y"
                defaultChecked={commVo.subValYn === "Y"}
                onChange={handleSubValYn}
                className="h-4 w-4 accent-blue-600"
              />
              <label htmlFor="subValY" className="flex items-center gap-2">
                <span>사용</span>
              </label>

              <Input
                type="radio"
                id="subValN"
                name="subValYn"
                value="N"
                defaultChecked={commVo.subValYn === "N"}
                onChange={handleSubValYn}
                className="h-4 w-4 accent-blue-600"
              />
              <label htmlFor="subValN" className="flex items-center gap-2">
                <span>미사용</span>
              </label>
            </div>
          </div>

          {/* 보조통계 사용 시 보조통계 선택 UI */}
          {commVo.subValYn === "Y" && (
            <>
              <div>
                <div className="mb-2 mt-2 border-gray-300 bg-gray-200 font-semibold">
                  ▣ 보조통계 선택
                </div>
                <div className="mb-2 flex items-center gap-2">
                  <Input
                    type="radio"
                    id="subPresetY"
                    name="subPresetYn"
                    value="Y"
                    checked={commVo.subPresetYn === "Y"}
                    onChange={handleSubPresetYn}
                    className="h-4 w-4 accent-blue-600"
                  />
                  <label
                    htmlFor="subPresetY"
                    className="flex items-center gap-2"
                  >
                    <span>프리셋 선택</span>
                  </label>

                  <Input
                    type="radio"
                    id="subPresetN"
                    name="subPresetYn"
                    value="N"
                    checked={commVo.subPresetYn === "N"}
                    onChange={handleSubPresetYn}
                    className="h-4 w-4 accent-blue-600"
                  />
                  <label
                    htmlFor="subPresetN"
                    className="flex items-center gap-2"
                  >
                    <span>직접 입력</span>
                  </label>
                </div>

                {/* 프리셋 선택 UI */}
                {commVo.subPresetYn === "Y" ? (
                  <>
                    <div className="mb-2 flex items-center font-semibold">
                      <Select
                        onValueChange={handleSubPresetList}
                        value={commVo.subPresetVal}
                      >
                        <SelectTrigger className="w-40">
                          <SelectValue placeholder="선택" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>선택</SelectLabel>
                            <SelectItem value="1">샘플</SelectItem>
                            {commVo.subPresetList.map(
                              (item: PresetVo, i: number) => (
                                <SelectItem
                                  key={`subPreset_${i}`}
                                  value={item.code}
                                >
                                  {item.codeName}
                                </SelectItem>
                              ),
                            )}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                      <div className="ml-2 font-medium">
                        <span>단위: </span>
                        <span>{commVo.stsUnit}</span>
                      </div>
                    </div>

                    {/* 단위 조정 UI */}
                    <div className="mb-2">단위조정</div>
                    <div className="mb-5 flex items-center font-semibold">
                      <Select
                        onValueChange={handleSubPresetCalUnit}
                        value={commVo.subPresetCalUnit}
                      >
                        <SelectTrigger className="w-40">
                          <SelectValue placeholder="선택" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>선택</SelectLabel>
                            <SelectItem value="1">조정없음</SelectItem>
                            <SelectItem value="1000">X1,000</SelectItem>
                            <SelectItem value="10000">X1,0000</SelectItem>
                            <SelectItem value="100000">X1,00000</SelectItem>
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                ) : (
                  // 직접입력 시
                  <div className="mt-2 flex items-center gap-4">
                    <span>단위</span>
                    <Input
                      type="text"
                      id="inputStsUnit"
                      name="inputStsUnit"
                      value={commVo.stsUnit}
                      placeholder="단위 입력"
                      onChange={handleInputStsUnit}
                      inputMode="numeric"
                      className="w-40"
                    />
                  </div>
                )}
              </div>

              {/* 계산식 선택 UI */}
              <div>
                <div className="mb-2 mt-2 border-gray-300 bg-gray-200 font-semibold">
                  ▣ 계산식 선택
                </div>
                <div className="flex flex-wrap items-center gap-2">
                  <Input
                    type="radio"
                    id="calFormulaT1"
                    name="calFormula"
                    value="T1"
                    checked={commVo.calFormula === "T1"}
                    onChange={handleCalFormula}
                    className="h-4 w-4 accent-blue-600"
                  />
                  <label htmlFor="calFormulaT1">
                    <span>
                      나누기
                      <br />
                      (A/B)
                    </span>
                  </label>

                  <Input
                    type="radio"
                    id="calFormulaT2"
                    name="calFormula"
                    value="T2"
                    checked={commVo.calFormula === "T2"}
                    onChange={handleCalFormula}
                    className="h-4 w-4 accent-blue-600"
                  />
                  <label htmlFor="calFormulaT2">
                    <span>
                      비율(%)
                      <br />
                      (A/B*100)
                    </span>
                  </label>

                  {commVo.sumYn === "Y" && (
                    <>
                      <Input
                        type="radio"
                        id="calFormulaT3"
                        name="calFormula"
                        value="T3"
                        checked={commVo.calFormula === "T3"}
                        onChange={handleCalFormula}
                        className="h-4 w-4 accent-blue-600"
                      />
                      <label htmlFor="calFormulaT3">
                        <span>
                          비중(%)
                          <br />
                          (A/D*100)
                        </span>
                      </label>
                    </>
                  )}

                  <Input
                    type="radio"
                    id="calFormulaT0"
                    name="calFormula"
                    value="T0"
                    checked={commVo.calFormula === "T0"}
                    onChange={handleCalFormula}
                    className="h-4 w-4 accent-blue-600"
                  />
                  <label htmlFor="calFormulaT0">
                    <span>
                      계산식
                      <br />
                      미적용
                    </span>
                  </label>
                </div>
              </div>
            </>
          )}
        </div>
        <StatsDataView />
      </AccordionContent>
    </AccordionItem>
  );
}
