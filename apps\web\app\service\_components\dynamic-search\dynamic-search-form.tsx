"use client";

import { cn } from "@geon-ui/react/lib/utils";
import { Button } from "@geon-ui/react/primitives/button";
import { Calendar } from "@geon-ui/react/primitives/calendar";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@geon-ui/react/primitives/collapsible";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@geon-ui/react/primitives/dropdown-menu";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@geon-ui/react/primitives/form";
import { Input } from "@geon-ui/react/primitives/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@geon-ui/react/primitives/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@geon-ui/react/primitives/select";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import {
  Bookmark,
  CalendarIcon,
  ChevronDown,
  ChevronUp,
  RotateCcw,
  Search,
  Star,
  Trash2,
} from "lucide-react";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { SpatialSearchPanel } from "./spatial-search";
import type { SearchFieldSchema, ServiceSearchSchema } from "./types";

export type SavedSearch = {
  id: string;
  name: string;
  query: Record<string, any>;
  createdAt: Date;
};

// 필드 그룹 타입 정의
type FieldGroup = {
  fields: SearchFieldSchema[];
  columns: number; // 한 행에 배치할 컬럼 수
  label?: string;
};

// Form 스키마를 동적으로 생성하는 함수
function createFormSchema(fields: SearchFieldSchema[]) {
  const schemaFields: Record<string, z.ZodTypeAny> = {};

  for (const field of fields) {
    switch (field.type) {
      case "text":
        schemaFields[field.id] = z.string().optional();
        break;
      case "number":
        schemaFields[field.id] = z.string().optional(); // number input도 string으로 받음
        break;
      case "select":
        schemaFields[field.id] = z.string().optional();
        break;
      case "date":
        schemaFields[field.id] = z.date().optional();
        break;
      case "dateRange":
        schemaFields[field.id] = z
          .array(z.string().nullable())
          .length(2)
          .optional();
        break;
      case "buttonGroup":
        schemaFields[field.id] = z.string().optional();
        break;
      case "spatialSearch":
        schemaFields[field.id] = z.any().optional(); // SpatialSearchOptions or null
        break;
      default:
        schemaFields[field.id] = z.any().optional();
    }
  }

  return z.object(schemaFields);
}

// 필드들을 그룹화하는 로직
function groupFields(fields: SearchFieldSchema[]): FieldGroup[] {
  const groups: FieldGroup[] = [];
  let currentGroup: SearchFieldSchema[] = [];

  fields.forEach((field, index) => {
    // select, text, number 타입의 짧은 필드들은 2개씩 그룹화
    if (
      field.type === "select" ||
      field.type === "text" ||
      field.type === "number"
    ) {
      currentGroup.push(field);

      // 2개가 모이거나 마지막 필드인 경우 그룹 생성
      if (currentGroup.length === 2 || index === fields.length - 1) {
        groups.push({
          fields: [...currentGroup],
          columns: currentGroup.length,
        });
        currentGroup = [];
      }
    } else {
      // 현재 그룹에 필드가 있으면 먼저 추가
      if (currentGroup.length > 0) {
        groups.push({
          fields: [...currentGroup],
          columns: currentGroup.length,
        });
        currentGroup = [];
      }

      // 단일 필드 그룹 생성 (date, dateRange, buttonGroup, spatialSearch 등)
      groups.push({
        fields: [field],
        columns: 1,
      });
    }
  });

  return groups;
}

export type DynamicSearchFormProps = {
  schema: ServiceSearchSchema;
  value?: Record<string, any>;
  onChange?: (value: Record<string, any>) => void;
  onSubmit?: (value: Record<string, any>) => void;
  onSearch?: (value: Record<string, any>) => void; // 실시간 검색용
  className?: string;
  compact?: boolean;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
  // 저장된 검색 관리
  savedSearches?: SavedSearch[];
  onSaveSearch?: (name: string, query: Record<string, any>) => void;
  onLoadSearch?: (query: Record<string, any>) => void;
  onDeleteSearch?: (id: string) => void;
  // debounce 설정
  searchDebounceMs?: number;
  // 로딩 상태
  loading?: boolean;
  // 행정구역 변경 시 지도 하이라이트 콜백
  onAdminDistrictChange?: (districtCode: string) => void;
  // 초기화시 콜백
  onReset?: (value: Record<string, any>) => void;
};

function DynamicSearchForm({
  schema,
  value,
  onChange,
  onSubmit,
  // onSearch,
  className,
  compact = true,
  collapsible = true,
  defaultCollapsed = false,
  savedSearches = [],
  onSaveSearch,
  onLoadSearch,
  onDeleteSearch,
  // searchDebounceMs = 300,
  loading = false,
  onAdminDistrictChange,
  onReset,
}: DynamicSearchFormProps) {
  const formSchema = useMemo(
    () => createFormSchema(schema.fields),
    [schema.fields],
  );

  const initialValues = useMemo(
    () => buildInitialValues(schema.fields),
    [schema.fields],
  );

  const fieldGroups = useMemo(
    () => groupFields(schema.fields),
    [schema.fields],
  );

  // React Hook Form만 사용 (useActionState 제거)
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      ...initialValues,
      ...(value || {}),
    },
  });

  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);
  const [saveSearchName, setSaveSearchName] = useState("");

  // 폼 값들을 watch하고 debounce 적용
  const watchedValues = form.watch();
  // const [debouncedValues] = useDebounce(watchedValues, searchDebounceMs);

  // 실시간 검색 실행
  // useEffect(() => {
  //   if (onSearch && searchDebounceMs > 0) {
  //     onSearch(debouncedValues);
  //   }
  // }, [debouncedValues, onSearch, searchDebounceMs]);

  // onChange 콜백 실행
  useEffect(() => {
    onChange?.(watchedValues);
  }, [watchedValues, onChange]);

  // 행정구역 변경 시 지도 하이라이트 처리 (특정 필드만 watch)
  const adminDistrict = form.watch("adminDistrict");
  useEffect(() => {
    if (onAdminDistrictChange && adminDistrict) {
      onAdminDistrictChange(adminDistrict as string);
    }
  }, [adminDistrict, onAdminDistrictChange]);

  // value prop 변경시 form 업데이트
  useEffect(() => {
    if (value) {
      form.reset({ ...initialValues, ...value });
    }
  }, [value, form, initialValues]);

  const handleSubmit = form.handleSubmit((data) => {
    onSubmit?.(data);
  });

  const handleReset = useCallback(() => {
    form.reset(initialValues);
    onReset?.(initialValues);
  }, [form, initialValues]);

  const handleSaveSearch = useCallback(() => {
    if (saveSearchName.trim() && onSaveSearch) {
      const currentValues = form.getValues();
      onSaveSearch(saveSearchName.trim(), currentValues);
      setSaveSearchName("");
    }
  }, [saveSearchName, onSaveSearch, form]);

  const handleLoadSearch = useCallback(
    (savedSearch: SavedSearch) => {
      form.reset(savedSearch.query);
      onLoadSearch?.(savedSearch.query);
    },
    [form, onLoadSearch],
  );

  // SearchFormContent를 컴포넌트로 분리하여 최적화
  const SearchFormContent = useMemo(
    () => (
      <div className="space-y-4">
        {/* 그룹화된 검색 필드들 */}
        <div className="space-y-3">
          {fieldGroups.map((group, groupIndex) => (
            <div key={groupIndex}>
              {group.label && (
                <div className="text-muted-foreground mb-2 text-xs font-medium">
                  {group.label}
                </div>
              )}
              <div
                className={cn(
                  "grid gap-2",
                  group.columns === 1 && "grid-cols-1",
                  group.columns === 2 && "grid-cols-2",
                  group.columns === 3 && "grid-cols-3",
                )}
              >
                {group.fields.map((field) => (
                  <FormField
                    key={field.id}
                    control={form.control}
                    name={field.id}
                    render={({ field: formField }) => (
                      <FormItem className="space-y-1">
                        <FormLabel className="text-xs font-medium leading-none">
                          {field.label}
                        </FormLabel>
                        <FormControl>
                          <Field
                            field={field}
                            value={formField.value}
                            onChange={formField.onChange}
                            compact={compact}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* 저장된 검색 */}
        {savedSearches.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Bookmark className="h-3 w-3" />
              <span className="text-xs font-medium">저장된 검색</span>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size={compact ? "sm" : "default"}
                  className="h-7 w-full justify-between"
                >
                  <span className="text-xs">저장된 검색 불러오기</span>
                  <ChevronDown className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="w-56">
                {savedSearches.map((saved) => (
                  <DropdownMenuItem
                    key={saved.id}
                    className="flex items-center justify-between text-xs"
                    onClick={() => handleLoadSearch(saved)}
                  >
                    <span className="flex-1 truncate">{saved.name}</span>
                    {onDeleteSearch && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="hover:bg-destructive hover:text-destructive-foreground h-5 w-5 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          onDeleteSearch(saved.id);
                        }}
                      >
                        <Trash2 className="h-2 w-2" />
                      </Button>
                    )}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}

        {/* 검색 저장 */}
        {onSaveSearch && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Star className="h-3 w-3" />
              <span className="text-xs font-medium">검색 저장</span>
            </div>
            <div className="flex gap-2">
              <Input
                placeholder="검색명을 입력하세요..."
                value={saveSearchName}
                onChange={(e) => setSaveSearchName(e.target.value)}
                className="h-7 flex-1 text-xs"
              />
              <Button
                type="button"
                size="sm"
                onClick={handleSaveSearch}
                disabled={!saveSearchName.trim()}
                className="h-7 px-2"
              >
                <Star className="h-3 w-3" />
              </Button>
            </div>
          </div>
        )}

        {/* 액션 버튼들 */}
        <div className="flex gap-2 pt-2">
          <Button
            type="button"
            variant="outline"
            size={compact ? "sm" : "default"}
            className="h-7 flex-1"
            onClick={handleReset}
            disabled={loading}
          >
            <RotateCcw className="mr-1 h-3 w-3" />
            <span className="text-xs">초기화</span>
          </Button>
          <Button
            type="submit"
            size={compact ? "sm" : "default"}
            className="h-7 flex-1"
            disabled={loading}
          >
            <Search className="mr-1 h-3 w-3" />
            <span className="text-xs">{loading ? "검색 중..." : "검색"}</span>
          </Button>
        </div>
      </div>
    ),
    [
      fieldGroups,
      form.control,
      compact,
      savedSearches,
      saveSearchName,
      onSaveSearch,
      onDeleteSearch,
      handleReset,
      handleSaveSearch,
      handleLoadSearch,
      loading,
    ],
  );

  if (!collapsible) {
    return (
      <Form {...form}>
        <form onSubmit={handleSubmit} className={cn("space-y-3", className)}>
          {SearchFormContent}
        </form>
      </Form>
    );
  }

  return (
    <div className={className}>
      <Collapsible
        open={!isCollapsed}
        onOpenChange={(open) => setIsCollapsed(!open)}
      >
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-full justify-between rounded-none border-b px-3 py-2"
          >
            <span className="flex items-center gap-2 text-xs font-semibold">
              <Search className="h-3 w-3" />
              {schema.title || "검색"}
            </span>
            {isCollapsed ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronUp className="h-3 w-3" />
            )}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <Form {...form}>
            <form onSubmit={handleSubmit} className="space-y-3 p-3">
              {SearchFormContent}
            </form>
          </Form>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}

// 필드 컴포넌트 최적화
const Field = React.memo(function Field({
  field,
  value,
  onChange,
}: {
  field: SearchFieldSchema;
  value: any;
  onChange?: (v: any) => void;
  compact?: boolean;
}) {
  const handleChange = useCallback(
    (newValue: any) => {
      onChange?.(newValue);
    },
    [onChange],
  );

  switch (field.type) {
    case "text":
      return (
        <Input
          placeholder={field.placeholder}
          value={value ?? ""}
          onChange={(e) => handleChange(e.target.value)}
          className="h-7 text-xs"
        />
      );
    case "number":
      return (
        <Input
          type="number"
          placeholder={field.placeholder}
          value={value ?? ""}
          onChange={(e) => handleChange(e.target.value)}
          className="h-7 text-xs"
        />
      );
    case "select":
      return (
        <Select
          value={value ?? "ALL"}
          onValueChange={(v) => handleChange(v === "ALL" ? "" : v)}
        >
          <SelectTrigger className="h-7 w-full text-xs">
            <SelectValue placeholder="전체" />
          </SelectTrigger>
          <SelectContent>
            {(!field.options ||
              field.options.length === 0 ||
              !field.options.find((opt) => opt.value === "ALL")) && (
              <SelectItem key="ALL" value="ALL">
                전체
              </SelectItem>
            )}
            {field.options
              ?.filter((opt) => opt.value && opt.value.trim() !== "")
              .map((opt) => (
                <SelectItem key={opt.value} value={opt.value}>
                  {opt.label}
                </SelectItem>
              ))}
          </SelectContent>
        </Select>
      );
    case "date":
      return (
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "h-7 w-full justify-start text-left text-xs font-normal",
                !value && "text-muted-foreground",
              )}
            >
              <CalendarIcon className="mr-2 h-3 w-3" />
              {value ? (
                format(value, "PPP")
              ) : (
                <span>{field.placeholder || "날짜 선택"}</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={value}
              onSelect={handleChange}
              disabled={(date) =>
                date > new Date() || date < new Date("1900-01-01")
              }
              captionLayout="dropdown"
            />
          </PopoverContent>
        </Popover>
      );
    case "dateRange":
      return (
        <div className="grid grid-cols-2 gap-1">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "h-7 w-full justify-start text-left text-xs font-normal",
                  !value?.[0] && "text-muted-foreground",
                )}
              >
                <CalendarIcon className="mr-2 h-3 w-3" />
                {value?.[0] ? (
                  format(new Date(value[0]), "PPP")
                ) : (
                  <span>시작일</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={value?.[0] ? new Date(value[0]) : undefined}
                onSelect={(date) =>
                  handleChange([
                    date ? date.toISOString().split("T")[0] : null,
                    value?.[1] ?? null,
                  ])
                }
                disabled={(date) =>
                  date > new Date() || date < new Date("1900-01-01")
                }
                captionLayout="dropdown"
              />
            </PopoverContent>
          </Popover>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "h-7 w-full justify-start text-left text-xs font-normal",
                  !value?.[1] && "text-muted-foreground",
                )}
              >
                <CalendarIcon className="mr-2 h-3 w-3" />
                {value?.[1] ? (
                  format(new Date(value[1]), "PPP")
                ) : (
                  <span>종료일</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={value?.[1] ? new Date(value[1]) : undefined}
                onSelect={(date) =>
                  handleChange([
                    value?.[0] ?? null,
                    date ? date.toISOString().split("T")[0] : null,
                  ])
                }
                disabled={(date) =>
                  date > new Date() || date < new Date("1900-01-01")
                }
                captionLayout="dropdown"
              />
            </PopoverContent>
          </Popover>
        </div>
      );
    case "buttonGroup":
      return (
        <div className="flex flex-wrap gap-1">
          {field.buttons?.map((b) => (
            <Button
              key={b.id}
              type="button"
              size="sm"
              variant={value === b.id ? "default" : "outline"}
              className="h-6 px-2 text-xs"
              onClick={() => handleChange(value === b.id ? undefined : b.id)}
            >
              {b.label}
            </Button>
          ))}
        </div>
      );
    case "spatialSearch":
      return (
        <div className="pt-1">
          <SpatialSearchPanel
            value={value}
            onChange={handleChange}
            compact={true}
          />
        </div>
      );
    default:
      return null;
  }
});

DynamicSearchForm.displayName = "DynamicSearchForm";
Field.displayName = "DynamicSearchField";

export default DynamicSearchForm;

function buildInitialValues(fields: SearchFieldSchema[]) {
  const init: Record<string, any> = {};
  for (const f of fields) {
    if (f.defaultValue !== undefined) init[f.id] = f.defaultValue;
    else if (f.type === "select") init[f.id] = "";
    else init[f.id] = undefined;
  }
  return init;
}
