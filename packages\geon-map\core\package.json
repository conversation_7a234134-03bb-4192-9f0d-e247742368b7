{"name": "@geon-map/core", "version": "0.0.1", "type": "module", "exports": {".": {"development": {"types": "./src/index.ts", "default": "./src/index.ts"}, "types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "sideEffects": false, "files": ["dist/**", "src/**"], "scripts": {"build": "tsup", "dev:prod": "tsup --watch", "prepublishOnly": "pnpm run build", "test": "vitest", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "devDependencies": {"@config/eslint": "workspace:*", "@config/typescript": "workspace:*", "@geon-query/model": "workspace:*", "@types/node": "^22.18.6", "eslint": "^9.35.0", "tsup": "^8.5.0", "typescript": "^5.9.2", "vitest": "^3.2.4"}, "publishConfig": {"access": "restricted"}, "dependencies": {"html-to-image": "^1.11.13", "jspdf": "^3.0.2", "konva": "^9.3.22"}}