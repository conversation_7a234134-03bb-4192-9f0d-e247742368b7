"use client";

import { BaseLayerInfo, LayerManager } from "@geon-map/react-odf";
import { useCallback } from "react";

export interface ServiceLayerProviderProps {
  /** 배경 레이어 목록 */
  baseLayers: BaseLayerInfo[];
  /** 오버레이 레이어 목록 */
  overlayLayers: BaseLayerInfo[];
  /** 활성 배경 레이어 ID */
  activeBaseLayerId: string;
  /** 활성 오버레이 레이어 ID 목록 */
  activeOverlayLayerIds?: string[];
}

/**
 * 서비스 레이어 관리를 위한 Client Component
 * 
 * Server Component에서 event handler를 전달할 수 없으므로
 * 별도의 Client Component로 분리
 */
export function ServiceLayerProvider({
  baseLayers,
  overlayLayers,
  activeBaseLayerId,
  activeOverlayLayerIds = [],
}: ServiceLayerProviderProps) {
  
  // 배경지도 레이어 콜백 함수들
  const handleLayerReady = useCallback(
    (layerId: string, type: 'base' | 'overlay') => {
      console.log(`✅ ${type} layer ready:`, layerId);
    },
    [],
  );

  const handleLayerError = useCallback(
    (error: Error, layerId: string, type: 'base' | 'overlay') => {
      console.error(`❌ ${type} layer error:`, layerId, error);
    },
    [],
  );

  return (
    <LayerManager
      baseLayers={baseLayers}
      overlayLayers={overlayLayers}
      activeBaseLayerId={activeBaseLayerId}
      activeOverlayLayerIds={activeOverlayLayerIds}
      onLayerReady={handleLayerReady}
      onLayerError={handleLayerError}
    />
  );
}
