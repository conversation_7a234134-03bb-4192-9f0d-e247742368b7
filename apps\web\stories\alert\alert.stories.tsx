import { Alert } from "@geon-ui/react/primitives/alert";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import AlertDemoSource from "!!raw-loader!./demo";

import { AlertDemo } from "./demo";

const meta = {
  title: "Shadcn/Alert",
  component: Alert,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Alert>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: AlertDemoSource,
      },
    },
  },
  render: () => <AlertDemo />,
};
