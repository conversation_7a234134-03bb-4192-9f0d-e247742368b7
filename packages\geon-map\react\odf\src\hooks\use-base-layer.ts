import { useCallback, useMemo } from "react";

import { useStores } from "../contexts/map-store-context";
import type { BaseLayerInfo, Layer } from "../types/layer-types";
import { useMap } from "./use-map";

/**
 * 🎯 배경 레이어 통합 관리 훅
 *
 * 배경 레이어의 상태 조회와 액션을 모두 제공합니다.
 * ODF의 배경지도 기능을 사용하지 않고, 일반 레이어로 처리하여
 * React 상태 관리와 완전히 동기화됩니다.
 */
export function useBaseLayer() {
  const { layerStore } = useStores();
  const { map } = useMap();

  // 상태 구독 - 배경 레이어
  const currentBaseLayerId = layerStore((state) => state.currentBaseLayerId);
  const availableBaseLayers = layerStore((state) => state.availableBaseLayers);
  const baseLayersByCategory = layerStore(
    (state) => state.baseLayersByCategory,
  );

  // 상태 구독 - 오버레이/하이브리드 레이어
  const activeOverlayLayerIds = layerStore(
    (state) => state.activeOverlayLayerIds,
  );
  const availableOverlayLayers = layerStore(
    (state) => state.availableOverlayLayers,
  );
  const overlayLayersByCategory = layerStore(
    (state) => state.overlayLayersByCategory,
  );

  // 현재 활성 배경 레이어 정보
  const currentBaseLayer = useMemo(() => {
    return availableBaseLayers.find((layer) => layer.id === currentBaseLayerId);
  }, [availableBaseLayers, currentBaseLayerId]);

  // 모든 카테고리 목록 (배경 레이어)
  const categories = useMemo(() => {
    return Array.from(baseLayersByCategory.keys());
  }, [baseLayersByCategory]);

  // 🆕 오버레이 레이어 계산된 상태들

  // 현재 활성 오버레이 레이어들 정보
  const activeOverlayLayers = useMemo(() => {
    return activeOverlayLayerIds
      .map((id) => availableOverlayLayers.find((layer) => layer.id === id))
      .filter((layer): layer is BaseLayerInfo => layer !== undefined);
  }, [activeOverlayLayerIds, availableOverlayLayers]);

  // 오버레이 레이어 카테고리 목록
  const overlayCategories = useMemo(() => {
    return Array.from(overlayLayersByCategory.keys());
  }, [overlayLayersByCategory]);

  // 현재 활성 배경 레이어가 실제 지도에 표시되는지 확인
  const isCurrentBaseLayerVisible = useMemo(() => {
    if (!currentBaseLayerId) return false;

    const layer = layerStore.getState().getLayerById(currentBaseLayerId);
    return layer?.visible ?? false;
  }, [currentBaseLayerId, layerStore]);

  // 레이어 통계 (배경 레이어 + 오버레이 레이어)
  const stats = useMemo(
    () => ({
      // 배경 레이어 통계
      baseLayerCount: availableBaseLayers.length,
      baseCategoryCount: categories.length,
      hasActiveBaseLayer: !!currentBaseLayerId,
      isActiveBaseLayerVisible: isCurrentBaseLayerVisible,

      // 오버레이 레이어 통계
      overlayLayerCount: availableOverlayLayers.length,
      overlayCategoryCount: overlayCategories.length,
      activeOverlayLayerCount: activeOverlayLayerIds.length,

      // 전체 통계
      totalCount: availableBaseLayers.length + availableOverlayLayers.length,
      totalCategoryCount: categories.length + overlayCategories.length,
    }),
    [
      availableBaseLayers.length,
      categories.length,
      currentBaseLayerId,
      isCurrentBaseLayerVisible,
      availableOverlayLayers.length,
      overlayCategories.length,
      activeOverlayLayerIds.length,
    ],
  );

  // === 액션 메서드들 ===

  // 배경 레이어 전환
  const switchBaseLayer = useCallback(
    async (baseLayerId: string) => {
      const layerFactory = layerStore.getState().layerFactory;
      const baseLayerInfo = layerStore.getState().getBaseLayerById(baseLayerId);

      if (!layerFactory || !baseLayerInfo) {
        console.error("❌ Missing layerFactory or baseLayerInfo");
        return;
      }

      // 1. 모든 기존 배경 레이어들을 숨김
      const allLayers = layerStore.getState().layers;
      allLayers.forEach((layer) => {
        if (layer.isBaseLayer && layer.odfLayer) {
          layer.odfLayer.setVisible(false);
          layer.visible = false;
        }
      });

      // 2. 기존에 해당 배경 레이어가 이미 생성되어 있는지 확인
      const existingLayer = layerStore.getState().getLayerById(baseLayerId);

      if (existingLayer) {
        console.log("✅ Using existing layer:", baseLayerId);
        // 이미 존재하면 활성화하고 visible 설정
        existingLayer.odfLayer?.setVisible(true);
        existingLayer.visible = true;
        layerStore.getState().setCurrentBaseLayer(baseLayerId);
      } else {
        try {
          // 새로 생성
          const odfLayer = layerFactory.produce(
            "api",
            baseLayerInfo.layerParams,
          );
          odfLayer.setODFId(baseLayerId);

          const newLayer: Layer = {
            id: baseLayerId,
            name: baseLayerInfo.name,
            type: "baselayer",
            visible: true, // 새 레이어는 바로 보이게 설정
            zIndex: -1, // 배경 레이어는 가장 아래
            odfLayer,
            isBaseLayer: true,
            baseLayerInfo,
            params: baseLayerInfo.layerParams,
          };

          // Store에 추가 후 활성화
          layerStore.getState().addLayer(newLayer);
          layerStore.getState().setCurrentBaseLayer(baseLayerId);
          odfLayer.setMap(map);

          // Z-Index 설정 (배경 레이어는 가장 아래)
          if (odfLayer.setZIndex) {
            odfLayer.setZIndex(-1);
            console.log(`✅ Base layer z-index set to -1: ${baseLayerId}`);
          } else {
            console.warn(
              `⚠️ Base layer does not support setZIndex: ${baseLayerId}`,
            );
          }

          odfLayer.setVisible(true); // ODF 레이어도 visible 설정
        } catch (error) {
          console.error("❌ Error creating layer:", error);
        }
      }
    },
    [layerStore],
  );

  // 배경 레이어 옵션 추가 (API에서 가져온 데이터 등)
  const addBaseLayerOption = useCallback(
    (baseLayerInfo: BaseLayerInfo) => {
      layerStore.getState().addBaseLayerOption(baseLayerInfo);
    },
    [layerStore],
  );

  // 배경 레이어 옵션 제거
  const removeBaseLayerOption = useCallback(
    (baseLayerId: string) => {
      layerStore.getState().removeBaseLayerOption(baseLayerId);
    },
    [layerStore],
  );

  // 배경 레이어 가시성 토글
  const toggleBaseLayerVisibility = useCallback(
    (baseLayerId: string) => {
      const layer = layerStore.getState().getLayerById(baseLayerId);
      if (layer) {
        layerStore.getState().updateLayer(baseLayerId, {
          visible: !layer.visible,
        });
      }
    },
    [layerStore],
  );

  // 배경 레이어 제거 (레이어와 옵션 모두)
  const removeBaseLayer = useCallback(
    (baseLayerId: string) => {
      // 레이어 제거
      layerStore.getState().removeLayer(baseLayerId);
      // 옵션 제거
      layerStore.getState().removeBaseLayerOption(baseLayerId);
    },
    [layerStore],
  );

  // 여러 배경 레이어 옵션 일괄 설정
  const setAvailableBaseLayers = useCallback(
    (baseLayers: BaseLayerInfo[]) => {
      layerStore.getState().setAvailableBaseLayers(baseLayers);
    },
    [layerStore],
  );

  // 카테고리별 배경 레이어 목록 조회
  const getBaseLayersByCategory = useCallback(
    (category: string) => {
      return baseLayersByCategory.get(category) || [];
    },
    [baseLayersByCategory],
  );

  // ID로 배경 레이어 정보 조회
  const getBaseLayerById = useCallback(
    (baseLayerId: string) => {
      return (
        availableBaseLayers.find(
          (layer: BaseLayerInfo) => layer.id === baseLayerId,
        ) || null
      );
    },
    [availableBaseLayers],
  );

  // 🆕 오버레이/하이브리드 레이어 액션 메서드들

  // 오버레이 레이어 토글
  const toggleOverlayLayer = useCallback(
    async (overlayLayerId: string) => {
      console.log("🔄 toggleOverlayLayer called:", overlayLayerId);

      const layerFactory = layerStore.getState().layerFactory;
      const overlayLayerInfo = layerStore
        .getState()
        .getOverlayLayerById(overlayLayerId);
      const isCurrentlyActive = layerStore
        .getState()
        .isOverlayLayerActive(overlayLayerId);

      if (!layerFactory || !overlayLayerInfo) {
        console.error("❌ Missing layerFactory or overlayLayerInfo");
        return;
      }

      if (isCurrentlyActive) {
        // 비활성화: 레이어 숨기기 및 상태에서 제거
        console.log("👁️ Deactivating overlay layer:", overlayLayerId);
        layerStore.getState().toggleOverlayLayer(overlayLayerId);
      } else {
        // 활성화: 레이어 생성 및 표시
        const existingLayer = layerStore
          .getState()
          .getLayerById(overlayLayerId);

        if (existingLayer) {
          console.log("✅ Using existing overlay layer:", overlayLayerId);
          layerStore.getState().toggleOverlayLayer(overlayLayerId);
        } else {
          console.log(
            "🆕 Creating new overlay layer:",
            overlayLayerId,
            overlayLayerInfo.layerParams,
          );

          try {
            // 새로 생성
            const odfLayer = layerFactory.produce(
              "api",
              overlayLayerInfo.layerParams,
            );
            odfLayer.setODFId(overlayLayerId);

            const newLayer: Layer = {
              id: overlayLayerId,
              name: overlayLayerInfo.name,
              type: "baselayer", // 또는 "overlay" 타입 추가 필요
              visible: false, // 일단 숨김 상태로 생성
              zIndex: 1, // 오버레이는 배경레이어 위에
              odfLayer,
              isBaseLayer: false,
              baseLayerInfo: overlayLayerInfo,
              params: overlayLayerInfo.layerParams,
            };

            console.log("📦 Adding overlay layer to store:", newLayer);

            // Store에 추가 후 활성화
            layerStore.getState().addLayer(newLayer);
            layerStore.getState().toggleOverlayLayer(overlayLayerId);

            odfLayer.setMap(map);

            // Z-Index 설정 (오버레이 레이어는 배경레이어 위에)
            if (odfLayer.setZIndex) {
              odfLayer.setZIndex(1);
              console.log(
                `✅ Overlay layer z-index set to 1: ${overlayLayerId}`,
              );
            } else {
              console.warn(
                `⚠️ Overlay layer does not support setZIndex: ${overlayLayerId}`,
              );
            }

            console.log(
              "✅ Overlay layer added and activated:",
              overlayLayerId,
            );
          } catch (error) {
            console.error("❌ Error creating overlay layer:", error);
          }
        }
      }
    },
    [layerStore, map],
  );

  // 오버레이 레이어 옵션 추가
  const addOverlayLayerOption = useCallback(
    (overlayLayerInfo: BaseLayerInfo) => {
      layerStore
        .getState()
        .setAvailableOverlayLayers([
          ...layerStore.getState().availableOverlayLayers,
          overlayLayerInfo,
        ]);
    },
    [layerStore],
  );

  // 여러 오버레이 레이어 옵션 일괄 설정
  const setAvailableOverlayLayers = useCallback(
    (overlayLayers: BaseLayerInfo[]) => {
      layerStore.getState().setAvailableOverlayLayers(overlayLayers);
    },
    [layerStore],
  );

  // 카테고리별 오버레이 레이어 목록 조회
  const getOverlayLayersByCategory = useCallback(
    (category: string) => {
      return overlayLayersByCategory.get(category) || [];
    },
    [overlayLayersByCategory],
  );

  // ID로 오버레이 레이어 정보 조회
  const getOverlayLayerById = useCallback(
    (overlayLayerId: string) => {
      return (
        availableOverlayLayers.find(
          (layer: BaseLayerInfo) => layer.id === overlayLayerId,
        ) || null
      );
    },
    [availableOverlayLayers],
  );

  // 오버레이 레이어 활성 상태 확인
  const isOverlayLayerActive = useCallback(
    (overlayLayerId: string) => {
      return activeOverlayLayerIds.includes(overlayLayerId);
    },
    [activeOverlayLayerIds],
  );

  return {
    // === 배경 레이어 상태 (읽기) ===
    currentBaseLayerId,
    currentBaseLayer,
    isCurrentBaseLayerVisible,
    availableBaseLayers,
    categories,
    isReady: availableBaseLayers.length > 0,

    // === 오버레이 레이어 상태 (읽기) ===
    activeOverlayLayerIds,
    activeOverlayLayers,
    availableOverlayLayers,
    overlayCategories,

    // === 통계 정보 ===
    stats,

    // === 배경 레이어 조회 메서드 ===
    getBaseLayersByCategory,
    getBaseLayerById,

    // === 오버레이 레이어 조회 메서드 ===
    getOverlayLayersByCategory,
    getOverlayLayerById,
    isOverlayLayerActive,

    // === 배경 레이어 액션 메서드 ===
    switchBaseLayer,
    addBaseLayerOption,
    removeBaseLayerOption,
    toggleBaseLayerVisibility,
    removeBaseLayer,
    setAvailableBaseLayers,

    // === 오버레이 레이어 액션 메서드 ===
    toggleOverlayLayer,
    addOverlayLayerOption,
    setAvailableOverlayLayers,
  };
}
