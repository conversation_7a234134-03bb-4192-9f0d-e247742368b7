"use client";

import { BaseLayer, BaseLayerInfo } from "@geon-map/react-odf";
import { useCallback } from "react";

export interface BaseLayerProviderProps {
  /** 배경 레이어 목록 */
  baseLayers: BaseLayerInfo[];
  /** 오버레이 레이어 목록 */
  overlayLayers: BaseLayerInfo[];
  /** 활성 배경 레이어 ID */
  activeBaseLayerId: string;
}

/**
 * 배경지도 관리를 위한 Client Component
 *
 * Server Component에서 event handler를 전달할 수 없으므로
 * 별도의 Client Component로 분리
 */
export function BaseLayerProvider({
  baseLayers,
  overlayLayers,
  activeBaseLayerId,
}: BaseLayerProviderProps) {
  // 배경지도 레이어 콜백 함수들
  const handleLayerReady = useCallback((layerId: string) => {
    console.log(`✅ Base layer ready: ${layerId}`);
  }, []);

  const handleLayerError = useCallback((error: Error) => {
    console.error(`❌ Base layer error:`, error);
  }, []);

  return (
    <>
      {/* 배경지도들 */}
      {baseLayers.map((baseLayerInfo) => (
        <BaseLayer
          key={baseLayerInfo.id}
          baseLayerInfo={baseLayerInfo}
          active={baseLayerInfo.id === activeBaseLayerId}
          onLayerReady={handleLayerReady}
          onLayerError={handleLayerError}
        />
      ))}

      {/* 오버레이들 */}
      {overlayLayers.map((overlayLayerInfo) => (
        <BaseLayer
          key={overlayLayerInfo.id}
          baseLayerInfo={overlayLayerInfo}
          active={false} // 오버레이는 기본적으로 비활성
          onLayerReady={handleLayerReady}
          onLayerError={handleLayerError}
        />
      ))}
    </>
  );
}
