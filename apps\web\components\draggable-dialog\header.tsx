import { useDraggable } from "@dnd-kit/core";
import { DialogHeader } from "@geon-ui/react/primitives/dialog";
import React, { ComponentProps, useId } from "react";

export default function DraggableDialogHeader({
  children,
  ...props
}: ComponentProps<typeof DialogHeader>) {
  const id = useId();
  const { attributes, listeners, setNodeRef } = useDraggable({ id });

  return (
    <DialogHeader
      className="cursor-move select-none"
      {...props}
      ref={setNodeRef}
      {...attributes}
      {...listeners}
    >
      {children}
    </DialogHeader>
  );
}
