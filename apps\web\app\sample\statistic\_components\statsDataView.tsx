"use client";

import { Input } from "@geon-ui/react/primitives/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@geon-ui/react/primitives/table";
import * as React from "react";

import { StatiscVo } from "../_type/commVo";
import { useStatsContext } from "./statsProvider";

/**
 * @description 통계 데이터 뷰
 */
export default function StatsDataView() {
  const { commVo, setCommVo, commVoClone, commVoApply } = useStatsContext();

  // 동적 input을 통해 색상 정보를 받기 위해 사용
  const refs = React.useRef<Array<HTMLInputElement | null>>([]);

  // 숫자 형식 검증: 정수 or 소수 (0.1, 10, 0 등)
  function decimalVal(inputVal: string): string {
    const isValidNumberFormat = /^(\d+)?(\.\d*)?$/.test(inputVal);

    if (!isValidNumberFormat) return "0";

    if (inputVal === ".") return "0";

    // 앞자리 0 제거 (단, "0" 또는 "0.xxx"는 유지)
    if (/^0\d+/.test(inputVal)) {
      inputVal = inputVal.replace(/^0+/, "");
    }

    return inputVal;
  }

  // 통계값 입력
  function handleVal(e: React.ChangeEvent<HTMLInputElement>, index: number) {
    const inputVal = decimalVal(e.target.value);
    const vo = commVoClone();
    vo.dataList[index].stsVal = inputVal;
    commVoApply(vo);
  }

  // 보조값 입력
  function handleSubVal(e: React.ChangeEvent<HTMLInputElement>, index: number) {
    const inputVal = decimalVal(e.target.value);
    const vo = commVoClone();
    vo.dataList[index].subVal = inputVal;

    // 입력값이 콤마로 끝날때는 계산을 하지 않게 처리
    if (/^\d+\.$/.test(String(inputVal)) || /\.0+$/.test(String(inputVal))) {
      setCommVo(vo);
    } else {
      commVoApply(vo);
    }
  }

  // 사용자 색상 선택
  function handleInputColor(index: number) {
    refs.current[index]?.click();
  }
  function handleUserColorChange(
    e: React.ChangeEvent<HTMLInputElement>,
    index: number,
  ) {
    const inputVal = e.target.value;
    const vo = commVoClone();
    vo.dataList[index].color = inputVal;
    vo.dataList[index].userClr = inputVal;
    commVoApply(vo);
  }

  return (
    <div className="border-t border-gray-300">
      {commVo.dataList.length > 0 ? (
        <Table className="mt-2 w-full table-fixed border-b border-t border-gray-300">
          <TableHeader className="bg-gray-100">
            <TableRow>
              <TableHead className="p-2 text-center text-sm">
                행정구역명
              </TableHead>
              <TableHead className="p-2 text-center text-sm">
                통계값(A)
              </TableHead>
              <TableHead className="p-2 text-center text-sm">
                보조
                <br />
                통계값(B)
              </TableHead>
              <TableHead className="p-2 text-center text-sm">
                계산값(C)
              </TableHead>
              <TableHead className="p-2 text-center text-sm">색상</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {commVo.dataList.map((item: StatiscVo, index: number) => {
              return (
                <TableRow key={index}>
                  <TableCell className="text-sm">{item.codeName}</TableCell>
                  <TableCell>
                    <Input
                      type="text"
                      id={"stsVal" + index}
                      name={"stsVal" + index}
                      value={item.stsVal}
                      placeholder="통계값"
                      onChange={(e) => handleVal(e, index)}
                      inputMode="decimal"
                      className="text-sm"
                    />
                  </TableCell>
                  <TableCell>
                    <Input
                      type="text"
                      id={"subVal_" + index}
                      name={"subVal_" + index}
                      value={item.subVal}
                      placeholder="보조통계값"
                      readOnly={commVo.subValYn == "N"}
                      onChange={(e) => handleSubVal(e, index)}
                      inputMode="decimal"
                      className="text-sm"
                    />
                  </TableCell>
                  <TableCell>
                    <Input
                      type="text"
                      id={"calVal" + index}
                      name={"calVal" + index}
                      value={item.calVal}
                      inputMode="decimal"
                      placeholder="계산값"
                      className="text-sm"
                      readOnly
                    />
                  </TableCell>
                  <TableCell>
                    <div
                      onClick={() => handleInputColor(index)}
                      style={{
                        whiteSpace: "nowrap",
                        overflow: "hidden",
                        width: "30px",
                        height: "30px",
                        border: "solid 1px",
                        backgroundColor: item.color,
                      }}
                    >
                      <input
                        ref={(el) => {
                          refs.current[index] = el;
                        }}
                        onChange={(e) => handleUserColorChange(e, index)}
                        type="color"
                        style={{
                          width: "0px",
                          height: "0px",
                        }}
                      />
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
            {commVo.sumYn == "Y" ? (
              <TableRow>
                <TableCell className="text-sm">합계(D)</TableCell>
                <TableCell>
                  <Input
                    type="text"
                    placeholder="통계값-합계"
                    readOnly
                    value={commVo.stsTotalVal}
                    className="text-sm"
                  />
                </TableCell>
                <TableCell>
                  <Input
                    type="text"
                    placeholder="보조통계값-합계"
                    readOnly
                    value={commVo.subTotalVal}
                    inputMode="decimal"
                    className="text-sm"
                  />
                </TableCell>
                <TableCell>
                  <Input
                    type="text"
                    placeholder="계산값-합계"
                    readOnly
                    value={commVo.calTotalVal}
                    inputMode="decimal"
                    className="text-sm"
                  />
                </TableCell>
                <TableCell></TableCell>
              </TableRow>
            ) : (
              ""
            )}
          </TableBody>
        </Table>
      ) : (
        ""
      )}
    </div>
  );
}
