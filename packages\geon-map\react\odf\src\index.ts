"use client";

// Components
export { BaseLayer, Layer } from "./components/layer";
export { Legend } from "./components/legend";
export { MainMap, Map, MapContainer } from "./components/map";
export { Popup } from "./components/popup";

// Hooks
export { useAreaDownload } from "./hooks/use-area-download";
export { useAreaPrint } from "./hooks/use-area-print";
export { useBaseLayer } from "./hooks/use-base-layer";
export { useDownload } from "./hooks/use-download";
export { useDraw } from "./hooks/use-draw";
export { useFeature } from "./hooks/use-feature";
export {
  createStylePreset,
  STYLE_PRESETS,
  useFeatureActions,
} from "./hooks/use-feature-actions";
export {
  determineLayerTypeByODFId,
  getODFLayerId,
  useLayer,
} from "./hooks/use-layer";
export { useMap } from "./hooks/use-map";
export { useMapActions } from "./hooks/use-map-actions";
export { useMapCapture } from "./hooks/use-map-capture";
export { useOverview } from "./hooks/use-overview";
export { usePrint } from "./hooks/use-print";
export { useProjection } from "./hooks/use-projection";
export { useScale } from "./hooks/use-scale";

// Draw Types
export type { UseDrawNewOptions } from "./hooks/use-draw";

// Controls Provider
export {
  type ControlsConfig,
  ControlsProvider,
  type ControlsProviderOptions,
  useControlsConfig,
} from "./providers/controls-provider";
export {
  ClearProvider,
  type ClearProviderOptions,
} from "./providers/draw/clear-provider";
export {
  DrawProvider,
  type DrawProviderOptions,
  useDrawProviderStatus,
} from "./providers/draw/draw-provider";
export {
  MeasureProvider,
  type MeasureProviderOptions,
} from "./providers/draw/measure-provider";
export {
  BaseLayerProvider,
  type BaseLayerProviderOptions,
} from "./providers/map/base-layer-provider";
export {
  OverviewProvider,
  type OverviewProviderOptions,
} from "./providers/map/overview-provider";
export {
  ScaleProvider,
  type ScaleProviderOptions,
} from "./providers/map/scale-provider";
export {
  MapProvider,
  useMapConfig,
  useMapProviderStatus,
} from "./providers/map-provider";

// Contexts (🆕 다중 지도 인스턴스 지원)
export {
  type MapStoreContextType,
  type MapStoreInitialOptions,
  MapStoreProvider,
  useStores,
} from "./contexts/map-store-context";

// Stores
export {
  createDrawStore, // 🆕 팩토리 함수
  useDrawActions,
  useDrawState,
  useDrawStore,
} from "./stores/draw-store";
export {
  createEventStore, // 🆕 팩토리 함수
  useEventState,
  useEventStoreActions,
} from "./stores/event-store";
export {
  createLayerStore, // 🆕 팩토리 함수
  useLayerStore,
} from "./stores/layer-store";
export {
  createMapStore, // 🆕 팩토리 함수
  useMapState,
} from "./stores/map-store"; // 새로운 통합 Store
export { logger } from "./stores/middleware/logger";
// Event Management
export {
  type LayerDetectionResult,
  type LayerFeatureInfo,
  type LayerQueueItem,
  useEvent,
} from "./hooks/use-event";

// Utils
export {
  changeFeatureStyle,
  deleteFeature,
  extractFeatureId,
  featureToWKT,
  validateStyleOptions,
} from "./utils/feature-actions";

// Types
export type * from "./types/event-types";
export type * from "./types/layer-types";
export type * from "./types/map-types";
export type * from "./types/marker-types";
export type * from "./types/popup-types";

// Layer types
export type {
  BaseLayerProps,
  LayerConfig,
  LayerProps,
  LayerState,
} from "./components/layer";

// Explicit exports for commonly used types
export type { BasemapId } from "./types/map-types";
