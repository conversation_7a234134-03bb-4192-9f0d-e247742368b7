export type LayerType =
  | "geoserver"
  | "geojson"
  | "kml"
  | "csv"
  | "geotiff"
  | "api"
  | "svg"
  | "empty";

export interface LayerConfig {
  type: LayerType;
  id?: string;
  name?: string;
  visible?: boolean;
  opacity?: number;
  zIndex?: number;
  autoFit?: boolean;
  fitDuration?: number;
  renderOptions?: {
    style?: any;
  };
}

export interface GeoserverLayerConfig extends LayerConfig {
  type: "geoserver";
  layer: string;
  server:
    | string
    | {
        url: string;
        version?: string;
        proxyURL?: string;
        proxyParam?: string;
      };
  service: "wms" | "wfs";
  method?: "get" | "post";
  bbox?: boolean;
  crtfckey?: string;
  projection?: string;
  limit?: number;
  tiled?: boolean;
  geometryType?: string;
  serviceTy?: string;
  cqlFilter?: string;
  info?: {
    lyrId: string;
    lyrNm: string;
  };
}

export interface GeoJsonLayerConfig extends LayerConfig {
  type: "geojson";
  data: any;
  dataProjectionCode?: string;
  featureProjectionCode?: string;
  service?: string;
}

export interface LayerCreationResult {
  success: boolean;
  layer?: AbstractLayer;
  error?: Error;
  type: "created" | "duplicate" | "failed";
  duplicateLayerId?: string;
}

export interface LayerUpdateOptions {
  visible?: boolean;
  opacity?: number;
  zIndex?: number;
  filter?: string;
  style?: any;
}

// Abstract Layer 인터페이스
export abstract class AbstractLayer {
  protected odfLayer: any;
  protected config: LayerConfig;
  protected id: string;

  constructor(odfLayer: any, config: LayerConfig) {
    this.odfLayer = odfLayer;
    this.config = config;
    this.id = odfLayer.getODFId();
  }

  // 공통 인터페이스
  getId(): string {
    return this.id;
  }

  getConfig(): LayerConfig {
    return { ...this.config };
  }

  getODFLayer(): any {
    return this.odfLayer;
  }

  // 추상 메서드들 - 각 구현체에서 구현해야 함
  abstract setVisible(visible: boolean): void;
  abstract setOpacity(opacity: number): void;
  abstract getOpacity(): number | null;
  abstract setZIndex(zIndex: number): void;
  abstract updateFilter(filter: string): void;
  abstract updateStyle(style: any): void;
  abstract fit(duration?: number): void;
  abstract clearFeatures(): void;
  abstract getLegendUrl(options?: any): string | null;

  // 공통 메서드
  update(options: LayerUpdateOptions): void {
    if (options.visible !== undefined) {
      this.setVisible(options.visible);
      this.config.visible = options.visible;
    }

    if (options.opacity !== undefined) {
      this.setOpacity(options.opacity);
      this.config.opacity = options.opacity;
    }

    if (options.zIndex !== undefined) {
      this.setZIndex(options.zIndex);
      this.config.zIndex = options.zIndex;
    }

    if (options.filter !== undefined) {
      this.updateFilter(options.filter);
    }

    if (options.style !== undefined) {
      this.updateStyle(options.style);
    }
  }

  // 안전한 메서드 실행 헬퍼
  protected safeExecute<T>(operation: () => T, errorMessage: string): T | null {
    try {
      return operation();
    } catch (error) {
      console.error(`[${this.constructor.name}] ${errorMessage}:`, error);
      return null;
    }
  }
}
