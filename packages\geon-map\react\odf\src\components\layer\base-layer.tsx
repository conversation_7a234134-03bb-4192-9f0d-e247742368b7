"use client";

import { useBaseLayer } from "@geon-map/react-odf";
import { useEffect, useMemo, useRef } from "react";

import type { BaseLayerInfo } from "../../types/layer-types";

export interface BaseLayerProps {
  /** 배경지도 정보 */
  baseLayerInfo: BaseLayerInfo;
  /** 활성화 여부 */
  active?: boolean;
  /** 레이어 준비 완료 콜백 */
  onLayerReady?: (layerId: string) => void;
  /** 레이어 오류 콜백 */
  onLayerError?: (error: Error) => void;
}

/**
 * 선언적 API로 배경지도 레이어를 관리하는 컴포넌트
 *
 * @example
 * ```tsx
 * <BaseLayer
 *   baseLayerInfo={eMapBasicInfo}
 *   active={true}
 *   onLayerReady={(layerId) => console.log('Base layer ready:', layerId)}
 * />
 * ```
 */
export function BaseLayer({
  baseLayerInfo,
  active = false,
  onLayerReady,
  onLayerError,
}: BaseLayerProps) {
  const { switchBaseLayer, currentBaseLayerId, getBaseLayerById } = useBaseLayer();
  
  // 이전 active 상태를 추적
  const prevActiveRef = useRef<boolean>(active);
  const isFirstMount = useRef(true);

  // 메모이제이션된 레이어 ID
  const layerId = useMemo(() => baseLayerInfo.id, [baseLayerInfo.id]);

  // active 상태 변경 감지
  const activeChanged = prevActiveRef.current !== active;

  // 컴포넌트 마운트 시에만 로그
  if (isFirstMount.current) {
    console.log(`[BaseLayer:${layerId}] 🔄 Component Mounted (active: ${active})`);
    isFirstMount.current = false;
  }

  // active 상태 변경 시 배경지도 전환
  useEffect(() => {
    let isCancelled = false;

    const handleBaseLayerSwitch = async () => {
      if (activeChanged || (active && currentBaseLayerId !== layerId)) {
        try {
          if (active) {
            console.log(`[BaseLayer:${layerId}] 🔄 Switching to base layer`);
            await switchBaseLayer(layerId);

            if (!isCancelled) {
              // 🆕 초기 속성 적용 (LayerFactory에서 하지 않으므로 여기서 처리)
              const layer = getBaseLayerById(layerId);
              if (layer) {
                // 배경지도는 기본적으로 visible=true, zIndex=-1, opacity=1
                // 이미 switchBaseLayer에서 처리되지만 명시적으로 확인
                console.log(`[BaseLayer:${layerId}] 🎯 Base layer properties applied`);
              }

              onLayerReady?.(layerId);
              console.log(`[BaseLayer:${layerId}] ✅ Base layer activated`);
            }
          }
        } catch (error) {
          if (!isCancelled) {
            console.error(`[BaseLayer:${layerId}] ❌ Failed to switch base layer:`, error);
            const layerError = error instanceof Error ? error : new Error(String(error));
            onLayerError?.(layerError);
          }
        }
      }
    };

    handleBaseLayerSwitch();

    // 이전 값 업데이트
    prevActiveRef.current = active;

    return () => {
      isCancelled = true;
    };
  }, [layerId, active, activeChanged, currentBaseLayerId, switchBaseLayer, onLayerReady, onLayerError]);

  // 컴포넌트 언마운트 시 로그
  useEffect(() => {
    return () => {
      console.log(`[BaseLayer:${layerId}] 🧹 Component unmounting`);
    };
  }, [layerId]);

  // 이 컴포넌트는 UI를 렌더링하지 않음 (레이어 관리만)
  return null;
}
