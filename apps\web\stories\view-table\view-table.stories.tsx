import { Badge } from "@geon-ui/react/primitives/badge";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import { createColumnHelper } from "@tanstack/react-table";

import ViewTable from "@/components/table/view";

// Sample data types
interface Person {
  id: string;
  firstName: string;
  lastName: string;
  age: number;
  email: string;
  status: "active" | "inactive";
}

// Sample data
const samplePeople: Person[] = [
  {
    id: "1",
    firstName: "<PERSON>",
    lastName: "Doe",
    age: 32,
    email: "<EMAIL>",
    status: "active",
  },
  {
    id: "2",
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    age: 28,
    email: "<EMAIL>",
    status: "active",
  },
  {
    id: "3",
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    age: 45,
    email: "<EMAIL>",
    status: "inactive",
  },
  {
    id: "4",
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    age: 35,
    email: "<EMAIL>",
    status: "active",
  },
  {
    id: "5",
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    age: 29,
    email: "<EMAIL>",
    status: "inactive",
  },
];

// Column definitions
const person = createColumnHelper<Person>();
const peopleColumns = [
  person.accessor("firstName", {
    header: "First Name",
  }),
  person.accessor("lastName", {
    header: "Last Name",
  }),
  person.accessor("age", {
    header: "Age",
  }),
  person.accessor("email", {
    header: "Email",
  }),
  person.accessor("status", {
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      return (
        <Badge
          className={`font-semibold ${
            status === "active"
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          {status}
        </Badge>
      );
    },
  }),
];

const meta: Meta<typeof ViewTable<any, any>> = {
  title: "shadcn/ViewTable",
  component: ViewTable,
  parameters: {
    layout: "padded",
    docs: {
      subtitle: "A simple table for viewing data using TanStack React Table.",
    },
  },
  tags: ["autodocs"],
  argTypes: {
    columns: {
      description: "Column definitions for the table",
    },
    data: {
      description: "Array of data to display in the table",
    },
    pinHeader: {
      control: "boolean",
      description: "Whether to pin the header to the top when scrolling",
    },
    className: {
      control: "text",
      description: "Additional CSS classes to apply to the table",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic examples
export const Default: Story = {
  args: {
    columns: peopleColumns,
    data: samplePeople,
  },
};
