# Shadcn Dialog Draggable 설정

```tsx
"use client";

export default function SampleDialog() {
  // dialog open state
  const [open, setOpen] = useState(false);

  return (
    // modal: 배경 검정색 오버레이 (default true)
    <Dialog open={open} onOpenChange={setOpen} modal={false}>
      {/* @/components/draggable-dialog/content */}
      <DraggableDialogContent
        open={open}
        interactive // dialog 바깥 상호작용 가능여부
      >
        {/* @/components/draggable-dialog/header, drag handle 역할 */}
        <DraggableDialogHeader>
          {/* Dialog Title, Dialog Description */}
        </DraggableDialogHeader>
        {/* Dialog Content Here */}
      </DraggableDialogContent>
    </Dialog>
  )
}
```
