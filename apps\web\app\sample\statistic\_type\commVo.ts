import { PresetDataVo, PresetVo } from "./presetVo";

export type YesNo = "Y" | "N";

export type CalFormula = "T0" | "T1" | "T2" | "T3";

export interface StatiscVo {
  // 행정구역코드
  code: string;
  // 행정구역명칭
  codeName: string;
  // 통계값
  stsVal: number;
  // 보조통계값
  subVal: number;
  // 계산값
  calVal: number;
  // 색상
  color: string;
  // 사용자 지정 색상
  userClr?: string | null;

  // 도형 정보 wkt
  geom?: string;
}

export interface CommVo {
  // 년도 선택
  yearList: [];
  yearUnit: string;

  // 시, 시도, 읍면동
  regionList: [];
  regionUnit: string;

  // 영역 범위
  rangeList: [];
  rangeUnit: string;

  // 입력대상 지역
  targetList: [];

  // 단위 (예: "명", "백만원")
  stsUnit: string;

  // 합계 표시 유무 (Y: 합계 표시, N: 합계 미표시)
  sumYn: YesNo;

  // 보조통계 사용 유무 (Y: 보조통계 사용, N: 사용하지 않음)
  subValYn: YesNo;

  // 프리셋 선택 유무 (Y: 프리셋 사용, N: 사용하지 않음)
  subPresetYn: YesNo;

  // 선택된 프리셋 값
  subPresetVal: string;

  // 프리셋 리스트
  subPresetList: PresetVo[];

  // 프리셋 정리 리스트
  subPresetDataList: PresetDataVo[];

  // 프리셋 단위 조정 값 (기본값: 1)
  subPresetCalUnit: string;

  // 계산 공식 "T1", "T2", "T3" 중 하나 (예: "T1"은 A/B 계산)
  calFormula: CalFormula;

  // 행정구역 경계 색상 (기본값: "#ffff00")
  outLineColor: string;

  // 행정구역 경계 선 두께 (기본값: 3)
  outLineSize: number;

  // 행정구역 경계 투명도 (기본값: 1)
  outLineTrans: number;

  // 라벨 설정 - 지역명 표시 유무 (Y: 표시, N: 표시 안함)
  isLabelAdm: string;

  // 라벨 설정 - 통계값 표시 유무 (Y: 표시, N: 표시 안함)
  isLabelCal: string;

  // 라벨 폰트 (기본값: "Dotum")
  textFontWrit: string;

  // 라벨 폰트 크기 (기본값: "3")
  textFontSize: string;

  // 라벨 폰트 색상 (기본값: "#ffffff")
  textFontColor: string;

  // 라벨이 보이는 축적 (기본값: "0")
  textLabelLevel: string;

  // 급간 설정 - 급간 유형 (기본값: "EqInterval")
  statsType: string;

  // 급간 설정 - 급간 크기 (기본값: 10)
  statsRangeSize: string;

  // 급간 설정 - 급간 투명도 (기본값: 100)
  statsColorTrans: number;

  // 급간 색상
  statsColor: string;

  // 색상 리스트
  statsColorList: any[];

  // 행정구역별 통계값 데이터셋
  dataList: StatiscVo[];

  // 통계값 합
  stsTotalVal: number;

  // 보조통계값 합
  subTotalVal: number;

  // 계산값 합
  calTotalVal: number;

  // 레이어 아이디
  statsLayerId: string;

  // 레이어 스타일 JSON
  statsLayerStyle: string;

  // 레이어 발행 정보
  statsLayerNiceDetail: any;
}
