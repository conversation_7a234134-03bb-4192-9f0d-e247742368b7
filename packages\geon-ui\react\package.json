{"name": "@geon-ui/react", "version": "0.0.0", "private": true, "type": "module", "exports": {"./*": "./src/components/*.tsx", "./hooks/*": "./src/hooks/*.ts", "./lib/*": "./src/lib/*.ts", "./utils": "./src/lib/utils.ts"}, "scripts": {"lint": "eslint . --max-warnings 0", "generate:component": "turbo gen react-component", "check-types": "tsc --noEmit", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "devDependencies": {"@config/eslint": "workspace:*", "@config/typescript": "workspace:*", "@storybook/addon-docs": "^9.1.6", "@storybook/addon-links": "^9.1.6", "@storybook/addon-webpack5-compiler-swc": "4.0.1", "@storybook/react-webpack5": "^9.1.6", "@types/node": "^22.18.6", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "eslint": "^9.35.0", "eslint-plugin-storybook": "^9.1.6", "postcss-loader": "^8.2.0", "prettier-plugin-tailwindcss": "^0.6.14", "storybook": "^9.1.6", "typescript": "^5.9.2"}, "dependencies": {"@config/tailwind": "workspace:^", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@hookform/resolvers": "^5.2.2", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "lucide-react": "^0.544.0", "next-themes": "^0.4.6", "postcss": "^8.5.6", "react": "^19.1.1", "react-day-picker": "^9.10.0", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-resizable-panels": "^3.0.6", "recharts": "2.15.4", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^4.1.9"}}