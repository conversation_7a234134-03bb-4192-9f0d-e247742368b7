import { AbstractLayer, GeoserverLayerConfig } from "../layer-types";

export class GeoserverLayer extends AbstractLayer {
  protected config: GeoserverLayerConfig;

  constructor(odfLayer: any, config: GeoserverLayerConfig) {
    super(odfLayer, config);
    this.config = config;
  }

  setVisible(visible: boolean): void {
    this.safeExecute(
      () => this.odfLayer.setVisible(visible),
      `Failed to set visibility to ${visible}`,
    );
  }

  setOpacity(opacity: number): void {
    this.safeExecute(
      () => this.odfLayer.setOpacity(opacity),
      `Failed to set opacity to ${opacity}`,
    );
  }

  setZIndex(zIndex: number): void {
    this.safeExecute(
      () => this.odfLayer.setZIndex(zIndex),
      `Failed to set zIndex to ${zIndex}`,
    );
  }

  updateFilter(filter: string): void {
    this.safeExecute(() => {
      if (!filter || filter.trim() === "") {
        // 필터 초기화
        this.odfLayer.defineQuery({ condition: null });
      } else {
        // 필터 적용
        this.odfLayer.defineQuery({ condition: filter });
      }
    }, `Failed to update filter: ${filter}`);
  }

  updateStyle(style: any): void {
    this.safeExecute(() => {
      const parsedStyle = typeof style === "string" ? JSON.parse(style) : style;

      if (this.config.service === "wms") {
        // WMS는 SLD 스타일 사용
        if (this.odfLayer.setSLD) {
          // SLD 스타일 적용 (글로벌 ODF StyleFactory 필요)
          const sldStyle = (globalThis as any).odf?.StyleFactory?.produceSLD?.(
            parsedStyle,
          );
          if (sldStyle) {
            this.odfLayer.setSLD(sldStyle);
          }
        }
      } else {
        // WFS는 일반 스타일 사용
        if (this.odfLayer.setStyle) {
          const odfStyle = (globalThis as any).odf?.StyleFactory?.produce?.(
            parsedStyle,
          );
          if (odfStyle) {
            this.odfLayer.setStyle(odfStyle);
          }
        }
      }
    }, `Failed to update style`);
  }

  fit(duration: number = 0): void {
    this.safeExecute(() => this.odfLayer.fit(duration), `Failed to fit layer`);
  }

  // Geoserver 특화 메서드들
  setSLD(sld: any): void {
    this.safeExecute(() => {
      if (this.odfLayer.setSLD) {
        this.odfLayer.setSLD(sld);
      }
    }, "Failed to set SLD");
  }

  getCQLFilter(): string | null {
    return this.config.cqlFilter || null;
  }

  getLayerName(): string {
    return this.config.layer;
  }

  getServerUrl(): string {
    return typeof this.config.server === "string"
      ? this.config.server
      : this.config.server.url;
  }

  getService(): "wms" | "wfs" {
    return this.config.service;
  }

  // AbstractLayer 메서드 구현
  getOpacity(): number | null {
    try {
      return this.odfLayer.getOpacity?.() ?? null;
    } catch (error) {
      console.error(`[GeoserverLayer] Failed to get opacity:`, error);
      return null;
    }
  }

  clearFeatures(): void {
    this.safeExecute(
      () => this.odfLayer.clearFeatures?.(),
      `Failed to clear features`,
    );
  }

  getLegendUrl(options?: any): string | null {
    try {
      return this.odfLayer.getLegendUrl?.(options) ?? null;
    } catch (error) {
      console.error(`[GeoserverLayer] Failed to get legend URL:`, error);
      return null;
    }
  }
}
