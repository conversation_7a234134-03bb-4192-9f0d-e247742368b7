"use client";

import { useMap } from "@geon-map/react-odf";
import { HomeIcon } from "lucide-react";
import * as React from "react";

import { ToolbarItem, ToolbarTrigger } from "./base/toolbar-item";

// Props for ToolbarHome component
export interface ToolbarHomeProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 툴팁 텍스트 */
  tooltip?: string;
  /** 버튼 크기 */
  size?: "sm" | "lg" | "default" | "icon";
  /** 홈 위치 좌표 [경도, 위도] */
  center?: [number, number];
  /** 홈 줌 레벨 */
  zoom?: number;
  /** 애니메이션 지속시간 (ms) */
  duration?: number;
  /** 툴바 위치 (ToolbarContainer에서 전달) */
  position?:
    | "top-left"
    | "top-center"
    | "top-right"
    | "center-left"
    | "center-right"
    | "bottom-left"
    | "bottom-center"
    | "bottom-right";
}

// ToolbarHome Component - 단순 버튼형 컴포넌트
export const ToolbarHome = React.forwardRef<
  HTMLButtonElement,
  ToolbarHomeProps
>(
  (
    {
      tooltip = "홈 위치",
      size = "default",
      center = [145198.78246611584, 263662.28920716216], // 무안군
      zoom = 11,
      duration = 500,
      position,
      className,
      children,
      onClick,
      ...props
    },
    ref,
  ) => {
    const { map } = useMap();

    const handleHomeClick = React.useCallback(
      (e: React.MouseEvent<HTMLButtonElement>) => {
        if (map) {
          const view = map.getView();
          view.animate({
            center: center,
            zoom: zoom,
            duration: duration,
          });
        }
        onClick?.(e);
      },
      [map, center, zoom, duration, onClick],
    );

    return (
      <ToolbarItem position={position}>
        <ToolbarTrigger
          ref={ref}
          tooltip={tooltip}
          size={size}
          className={className}
          onClick={handleHomeClick}
          {...props}
        >
          {children || <HomeIcon className="h-4 w-4" />}
        </ToolbarTrigger>
      </ToolbarItem>
    );
  },
);

ToolbarHome.displayName = "ToolbarHome";
