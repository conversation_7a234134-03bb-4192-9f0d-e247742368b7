import geostats from "geostats";

import { ColorPallet, ColorPalletMap } from "../_type/colorPalletMapVo";

export const commUtils = {
  // 콤마
  getCommaVal(val: number) {
    if (!val) return val;
    const vals = val.toString().split(".");
    const result =
      Number(vals[0]).toLocaleString() + (vals[1] ? "." + vals[1] : "");
    return result;
  },
  // 숫자만
  toFloat(val: string) {
    val = String(val).replace(/[^0-9.-]/g, "");
    return isNaN(Number(val)) ? 0 : Number(val);
  },
  // 주어진 숫자를 고정 소수점으로 변환하는 함수 (기본 길이: 2자리)
  numberToFixed(val: number, len: number = 2): number {
    if (isNaN(val) || typeof val !== "number") return 0;
    if (len < 0) len = 0;
    return Number(val.toFixed(len));
  },

  // 통계 범위를 계산하는 함수 (EqInterval: 등간격, 다른 통계 방법도 지원)
  getStatsRange(
    statsType: string = "EqInterval",
    rangeSize: number = 10,
    values: number[],
  ): number[] | undefined {
    if (!values || values.length === 0) return;

    try {
      const geo = new geostats(values);
      const method = "getClass" + statsType;
      if (typeof geo[method] !== "function") {
        console.warn(
          `Unknown statsType "${statsType}". Defaulting to Equal Interval.`,
        );
        return geo.getClassEqInterval(rangeSize);
      }
      const range = geo[method](rangeSize);
      return range;
    } catch (e: unknown) {
      console.error(
        "⚠️ 예외 발생: 기하급수 등 일부 방식은 음수나 0이 포함된 데이터에 사용할 수 없습니다.",
        e,
      );
      return;
    }
  },

  // hex 값 만들기
  hexInAlpha(hex: string, alpha: number) {
    alpha = Math.round(alpha * 255);
    return hex.substring(0, 7) + alpha.toString(16).padStart(2, "0");
  },
  toHex(c: number): string {
    return c.toString(16).padStart(2, "0");
  },

  // rgba 값을 hex 형식으로 변환하는 함수
  rgbaToHex(r: number, g: number, b: number, a: number = 1): string {
    const alpha = Math.round(a * 255);
    return `#${this.toHex(r)}${this.toHex(g)}${this.toHex(b)}${this.toHex(alpha)}`.toUpperCase();
  },

  // 색상 팔레트에 따라 색상을 생성하는 함수
  generateColors(
    type: string,
    size = 10,
    alpha = 1,
    order = true,
  ): { rgb: string[]; hex: string }[] {
    if (typeof type !== "string" || !(type in colorPallet)) {
      throw console.error(
        "type 값은 'red', 'blue', 'yellow', 'green', 'purple', 'brown', 'black', 'random' 중 하나여야 합니다.",
      );
    }

    if (typeof order !== "boolean") {
      throw console.error("order 값은 true 또는 false여야 합니다.");
    }

    if (typeof alpha !== "number" || alpha < 0 || alpha > 1) {
      throw console.error("alpha 값은 0~1 범위의 숫자여야 합니다.");
    }

    const count = Math.floor(size);
    if (type === "random") {
      return this.produceRandomColor(count, alpha);
    }
    const palette: ColorPallet | undefined = colorPallet[type];
    if (!palette) {
      return this.produceRandomColor(count, alpha);
    }

    // 색상 간의 차이를 계산하여 점진적인 색상 생성
    const rStep = (palette.max[0] - palette.min[0]) / (count - 1);
    const gStep = (palette.max[1] - palette.min[1]) / (count - 1);
    const bStep = (palette.max[2] - palette.min[2]) / (count - 1);

    const colors: { rgb: string[]; hex: string }[] = [];

    for (let i = 0; i < count; i++) {
      const r = Math.round(
        order ? palette.min[0] + rStep * i : palette.max[0] - rStep * i,
      );
      const g = Math.round(
        order ? palette.min[1] + gStep * i : palette.max[1] - gStep * i,
      );
      const b = Math.round(
        order ? palette.min[2] + bStep * i : palette.max[2] - bStep * i,
      );

      const rgb: string[] = [
        r.toString(),
        g.toString(),
        b.toString(),
        alpha.toString(),
      ];
      const hex = this.rgbaToHex(r, g, b, alpha);

      colors.push({ rgb, hex });
    }

    return colors;
  },

  // 랜덤 색상을 생성하는 함수
  produceRandomColor(count = 1, alpha = 1) {
    if (typeof count !== "number" || count < 1) {
      throw console.error("count는 1 이상의 숫자여야 합니다.");
    }

    const randomChannel = () => Math.floor(Math.random() * 256);

    const colors: { rgb: string[]; hex: string }[] = [];

    // count 만큼 랜덤 색상 생성
    for (let i = 0; i < count; i++) {
      const r = randomChannel();
      const g = randomChannel();
      const b = randomChannel();
      const rgb: string[] = [
        r.toString(),
        g.toString(),
        b.toString(),
        alpha.toString(),
      ];
      const hex = this.rgbaToHex(r, g, b, alpha);
      colors.push({ rgb, hex });
    }
    return colors;
  },
};

// ColorPallet에 있는 색상 팔레트와 그 색상 범위 정의
const colorPallet: ColorPalletMap = {
  red2blue: {
    name: "빨간색→파란색",
    min: [33, 70, 255],
    max: [255, 75, 75],
  },
  red2green: {
    name: "빨간색→초록색",
    min: [112, 173, 71],
    max: [255, 75, 75],
  },
  red2purple: {
    name: "빨간색→보라색",
    min: [151, 81, 203],
    max: [255, 75, 75],
  },
  red2yellow: {
    name: "빨간색→노란색",
    min: [255, 255, 50],
    max: [255, 75, 75],
  },
  yellow2blue: {
    name: "노란색→파란색",
    min: [33, 70, 255],
    max: [255, 255, 50],
  },
  yellow2purple: {
    name: "노란색→보라색",
    min: [151, 81, 203],
    max: [255, 255, 50],
  },
  yellow2green: {
    name: "노란색→초록색",
    min: [112, 173, 71],
    max: [255, 255, 50],
  },
  yellow2brown: {
    name: "노란색→갈색",
    min: [132, 89, 12],
    max: [255, 255, 50],
  },
  yellow2black: {
    name: "노란색→검정색",
    min: [38, 38, 38],
    max: [255, 255, 50],
  },
  blue2green: {
    name: "파란색→초록색",
    min: [112, 173, 71],
    max: [33, 70, 255],
  },
  red: {
    name: "빨간색",
    min: [255, 75, 75],
    max: [255, 231, 231],
  },
  blue: {
    name: "파란색",
    min: [33, 70, 255],
    max: [231, 235, 255],
  },
  yellow: {
    name: "노란색",
    min: [255, 255, 50],
    max: [255, 255, 231],
  },
  green: {
    name: "초록색",
    min: [112, 173, 71],
    max: [237, 245, 231],
  },
  purple: {
    name: "보라색",
    min: [151, 81, 203],
    max: [239, 229, 247],
  },
  brown: {
    name: "갈색",
    min: [132, 89, 12],
    max: [250, 234, 206],
  },
  black: {
    name: "검정색",
    min: [38, 38, 38],
    max: [234, 234, 234],
  },
  random: {
    name: "랜덤",
    min: [0, 0, 0],
    max: [0, 0, 0],
  },
};
