"use client";

import { Layer } from "@geon-map/react-odf";
import { TOCNode } from "@geon-map/react-ui/types";
import { crtfckey, WMS_URL } from "@geon-query/model";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

import { useFacilitySearch } from "@/app/service/_contexts/facility-search";

/**
 * 서비스에 따른 레이어를 자동으로 관리하는 컴포넌트
 *
 * 역할:
 * - 선택된 서비스 ID에 따라 TOC 데이터 로드
 * - 로드된 TOC 데이터에서 레이어만 추출하여 Layer 컴포넌트로 렌더링
 * - CQL 필터 적용
 *
 * 배경지도는 ControlsProvider의 baseLayerOptions에서 관리됩니다.
 */
export function ServiceLayerManager() {
  const { selectedServiceId, appliedCQLFilter } = useFacilitySearch();
  const [layerNodes, setLayerNodes] = useState<TOCNode[]>([]);

  /**
   * 서비스별 TOC 데이터를 로드합니다.
   */
  const loadTOCData = useCallback(async (): Promise<TOCNode[]> => {
    if (!selectedServiceId) {
      return [];
    }

    try {
      // 공통 데이터는 항상 로드
      const commonModule = await import(`@/app/service/_data/toc/common`);
      const commonData = commonModule.TOC_DATA;

      try {
        // 서비스별 데이터 로드 시도
        const serviceModule = await import(
          `@/app/service/_data/toc/${selectedServiceId}`
        );
        const serviceData: TOCNode[] = serviceModule.TOC_DATA;
        console.log(
          `[ServiceLayerManager] 📁 TOC data loaded for service: ${selectedServiceId} (${serviceData.length + commonData.length} total layers)`,
        );

        // 서비스 데이터 + 공통 데이터 함께 반환
        return [...serviceData, ...commonData];
      } catch (serviceError) {
        // 서비스 데이터 로드 실패 시 공통 데이터만 반환
        console.warn(
          `[ServiceLayerManager] ⚠️ Service TOC data not found for: ${selectedServiceId}, using common layers only`,
        );
        return commonData;
      }
    } catch (error) {
      console.error(
        `[ServiceLayerManager] ❌ Failed to load TOC data for: ${selectedServiceId}`,
        error,
      );
      return [];
    }
  }, [selectedServiceId]);

  // 서비스 변경 시 레이어 추출 및 업데이트
  useEffect(() => {
    const extractLayers = async () => {
      if (!selectedServiceId) {
        setLayerNodes([]);
        return;
      }

      try {
        const tocData = await loadTOCData();
        const extractedLayerNodes: TOCNode[] = [];

        // TOC 데이터에서 레이어 노드만 추출
        const traverse = (nodes: TOCNode[]) => {
          nodes.forEach((node) => {
            if (node.type === "layer") {
              extractedLayerNodes.push(node);
            } else if (node.children) {
              traverse(node.children);
            }
          });
        };

        traverse(tocData);

        console.log(
          `[ServiceLayerManager] 🎯 Extracted ${extractedLayerNodes.length} layer nodes${appliedCQLFilter ? ` with CQL filter: ${appliedCQLFilter}` : ""}`,
        );

        setLayerNodes(extractedLayerNodes);
      } catch (error) {
        console.error(
          "[ServiceLayerManager] ❌ Failed to extract layers",
          error,
        );
        setLayerNodes([]);
      }
    };

    extractLayers();
  }, [selectedServiceId, loadTOCData]);

  // 🚀 메모이제이션된 공통 config 객체
  const baseConfig = useMemo(
    () => ({
      type: "geoserver" as const,
      server: { url: WMS_URL },
      crtfckey,
      service: "wms" as const,
      method: "post" as const,
      fit: false,
    }),
    [],
  );

  // 🚀 메모이제이션된 콜백 함수들
  const handleLayerReady = useCallback(
    (originalId: string) => (mapLayerId: string) => {
      console.log(
        `[ServiceLayerManager] ✅ Layer ready: ${originalId} -> ${mapLayerId}`,
      );
    },
    [],
  );

  const handleLayerError = useCallback(
    (originalId: string) => (error: Error) => {
      console.error(
        `[ServiceLayerManager] ❌ Layer error: ${originalId}`,
        error,
      );
    },
    [],
  );

  // 🚀 메모이제이션된 Layer 컴포넌트들
  const layerComponents = useMemo(() => {
    return layerNodes.map((layerNode) => (
      <Layer
        key={layerNode.id}
        id={layerNode.id}
        config={{
          ...baseConfig,
          layer: layerNode.id,
        }}
        visible={layerNode.visible ?? true}
        opacity={layerNode.opacity ?? 1}
        cqlFilter={appliedCQLFilter}
        onLayerReady={handleLayerReady(layerNode.id)}
        onLayerError={handleLayerError(layerNode.id)}
      />
    ));
  }, [
    layerNodes,
    baseConfig,
    appliedCQLFilter,
    handleLayerReady,
    handleLayerError,
  ]);

  // Layer 컴포넌트들을 렌더링
  return <>{layerComponents}</>;
}
