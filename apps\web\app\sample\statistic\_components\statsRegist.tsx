"use client";

import { useLayer } from "@geon-map/react-odf";
import { apiUrl, fetcher, url } from "@geon-query/model";
import { Button } from "@geon-ui/react/primitives/button";
import * as React from "react";

import { useStatsContext } from "./statsProvider";

/**
 * @description 레이어 발행
 */
export default function StatsRegist() {
  const { setCommVo, commVoClone } = useStatsContext();

  const { toGeoJson } = useLayer();

  // 통계레이어 저장
  async function handleRegist() {
    try {
      const vo = commVoClone();
      const layerId = vo.statsLayerId;
      if (!layerId) {
        console.log(`통계 레이어 ID 가 존재하지 않습니다`);
        return;
      }

      // TODO : opertUsrId, lyrNm 값들을 수정???
      // 레이어 발행
      const geoJson = toGeoJson(vo.statsLayerId);
      const response = await apiPublishLayer({
        featureText: geoJson,
        queryString: new URLSearchParams({
          srid: "5179",
          opertUsrId: "muan999",
          lyrNm: "muan999",
        }).toString(),
      });

      if (response?.code != 200) throw Error;
      const result = response?.result;
      const opertNtcnSn = result?.opertNtcnSn;

      // 작업 알림 조회
      const ntice = await apiNticeDetail({
        queryString: new URLSearchParams({ opertNtcnSn: opertNtcnSn }),
      });

      if (ntice?.code != 200) throw Error;
      vo.statsLayerNiceDetail = ntice?.result;
      setCommVo(vo);

      console.log(`레이어 등록 완료 = `, vo.statsLayerNiceDetail);
    } catch (error) {
      console.log("레이어 발행 오류");
      console.error(error);
    }
  }

  // 레이어 발행
  async function apiPublishLayer(params: any): Promise<any> {
    let _apiUrl = apiUrl({ endpoint: url.geojson.text, type: "publish" });
    _apiUrl = `${_apiUrl}&${params.queryString}`;
    return await fetcher.post(_apiUrl, params.featureText);
  }

  // 작업알림 조회
  async function apiNticeDetail(params: any): Promise<any> {
    let _apiUrl = apiUrl({ endpoint: url.ntice.detail, type: "analysis" });
    _apiUrl = `${_apiUrl}&${params.queryString}`;
    return await fetcher.get(_apiUrl, params);
  }

  return (
    <div>
      <div className="flex items-center justify-center gap-1">
        <Button
          onClick={handleRegist}
          className="mb-2 mt-2 h-8 w-60 bg-blue-500"
        >
          레이어 발행
        </Button>
      </div>
    </div>
  );
}
