import { createGeonMagpClient } from "@geon-query/model";
import { useAppMutation } from "@geon-query/react-query";

const client = createGeonMagpClient();

// 그룹별 테이블 ↔ 메서드 매핑
export const apiMethodMap = {
  water: {
    wtl_pipe_lm: "pipeSelect",
    wtl_sply_ls: "splySelect",
    wtl_manh_ps: "manhSelect",
    wtl_fire_ps: "fireSelect",
    wtl_prga_ps: "prgaSelect",
    wtl_stpi_ps: "stpiSelect",
    wtl_flow_ps: "flowSelect",
  },
  // road 그룹 추가 가능
} as const;

export type GroupName = keyof typeof apiMethodMap;

export function useApiMutation() {
  return useAppMutation({
    mutationFn: async (params: {
      group: GroupName;
      tableName: keyof (typeof apiMethodMap)[GroupName];
      gid: string;
    }) => {
      const { group, tableName, gid } = params;

      const method = apiMethodMap[group][
        tableName
      ] as keyof (typeof client)[typeof group];
      const fn = client[group][method] as (p: { gid: string }) => Promise<any>;

      const result = await fn({ gid });
      const table = await client.table.table({
        schema: "gcmagp",
        table: tableName as string,
      });

      return { result, table };
    },
  });
}
