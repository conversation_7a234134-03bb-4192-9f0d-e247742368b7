"use client";
import { useFeature, useLayer } from "@geon-map/react-odf";
import { useCallback, useEffect, useState } from "react";

type HighlightOptions = {
  isFitToLayer?: boolean;
  srid?: string;
  clear?: boolean;
  style?: any;
};
type UseHighlightLayerProps = {
  style?: any;
};

export function useHighlightLayer(props: UseHighlightLayerProps = {}) {
  const { style } = props;
  const {
    addLayer,
    addFeature,
    clearFeatures,
    fitToLayer,
    setMaxZIndex,
    updateLayerStyle,
  } = useLayer();
  const { fromWKT } = useFeature();
  const [layerId, setLayerId] = useState<string | null>(null);

  useEffect(() => {
    addLayer({ type: "empty" }).then((id: string | void) => {
      if (id) setLayerId(id);
    });
  }, [addLayer]);

  useEffect(() => {
    if (layerId) {
      if (style) {
        updateLayerStyle(layerId, style);
      } else {
        // 기본 스타일 적용
        updateLayerStyle(layerId, {
          "fill-color": [220, 60, 34, 0],
          "stroke-color": [255, 104, 68],
          "stroke-width": 5,
          "stroke-line-cap": "round",
          "stroke-line-join": "round",
        });
      }
    }
  }, [layerId]); // updateLayerStyle 제외

  const highlight = useCallback(
    (feature: any, options: HighlightOptions = {}) => {
      if (!feature || !layerId) return;

      const { isFitToLayer = true, srid, clear = true, style } = options;
      if (clear) clearFeatures(layerId);
      addFeature(layerId, feature, { srid });
      //TODO 움직이는게 느려서 500으로 설정. ux 의견 들어보고 조절 필요
      if (isFitToLayer) fitToLayer(layerId, 500);

      // TODO default style적용, 그러나 point,polygon,line에 따라 각각 스타일 처리가 필요할듯
      const appliedStyle = style ?? {
        "fill-color": [220, 60, 34, 0],
        "stroke-color": [30, 144, 255, 0.8],
        "stroke-width": 5,
      };

      updateLayerStyle(layerId, appliedStyle);
      setMaxZIndex(layerId);
    },
    [layerId, clearFeatures, addFeature, fitToLayer, setMaxZIndex],
  );

  const highlights = useCallback(
    (features: [], options: HighlightOptions = {}) => {
      features.map((feature) => {
        highlight(feature, options);
      });
    },
    [layerId, clearFeatures, addFeature, fitToLayer, setMaxZIndex],
  );

  const highlightWKT = useCallback(
    (wkt: string, options: HighlightOptions = {}) => {
      if (!wkt) return;
      const feature = fromWKT(wkt);
      highlight(feature, options);
    },
    [highlight],
  );

  const clearHighlight = useCallback(() => {
    if (!layerId) return;
    clearFeatures(layerId);
  }, [layerId, clearFeatures]);

  return {
    highlight,
    highlightWKT,
    clearHighlight,
    highlights,
    layerId,
    isReady: !!layerId,
  };
}
