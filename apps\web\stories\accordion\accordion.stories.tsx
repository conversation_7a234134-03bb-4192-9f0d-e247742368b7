import { Accordion } from "@geon-ui/react/primitives/accordion";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import AccordionDemoSource from "!!raw-loader!./demo";

import { AccordionDemo } from "./demo";

const meta = {
  title: "Shadcn/Accordion",
  component: Accordion,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Accordion>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {
    type: "single",
    collapsible: true,
    className: "w-full",
    defaultValue: "item-1",
  },
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: AccordionDemoSource,
      },
    },
  },
  render: () => <AccordionDemo />,
};
