"use client";

import { MapContainer } from "@geon-map/react-odf";
import { useBaseLayer, useMap } from "@geon-map/react-odf";
import {
  AddressSearch,
  AddressSearchContent,
  AddressSearchEmpty,
  AddressSearchInput,
  AddressSearchItem,
  AddressSearchList,
  AddressSearchSelect,
  AddressSearchTrigger,
  EupmyeondongSelector,
  LiSelector,
  RegionSelector,
  SidoSelector,
  SigunguSelector,
  SwipeDragHandle,
  ToolbarBasemap,
  ToolbarBasemapContent,
  ToolbarBasemapTrigger,
  ToolbarClear,
  ToolbarContainer,
  ToolbarDownload,
  ToolbarDownloadContent,
  ToolbarDownloadItem,
  ToolbarDownloadTrigger,
  ToolbarDraw,
  ToolbarDrawAction,
  ToolbarDrawContent,
  ToolbarDrawTool,
  ToolbarDrawTrigger,
  ToolbarHome,
  ToolbarMeasure,
  ToolbarMeasureAction,
  ToolbarMeasureContent,
  ToolbarMeasureTool,
  ToolbarMeasureTrigger,
  ToolbarPrint,
  ToolbarPrintContent,
  ToolbarPrintOriginalDialog,
  ToolbarPrintPaperDialog,
  ToolbarPrintTrigger,
  ToolbarSplit,
  ToolbarSplitContent,
  ToolbarSplitOption,
  ToolbarSplitTrigger,
  ToolbarSwipe,
  ToolbarSwipeContent,
  ToolbarSwipeTrigger,
  useAddressSearch,
  useRegionSelector,
  useSplitMode,
  ZoomControl,
} from "@geon-map/react-ui/components";
import { createGeonAddrgeoClient } from "@geon-query/model";
import { Button } from "@geon-ui/react/primitives/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@geon-ui/react/primitives/select";
import { Separator } from "@geon-ui/react/primitives/separator";
import { Slider } from "@geon-ui/react/primitives/slider";
import { UnfoldHorizontal } from "lucide-react";
import { useEffect, useState } from "react";

import { ServiceLayerManager } from "@/app/service/_components/widgets/service-layer-manager";
import SmartTOCWidgetService from "@/app/service/_components/widgets/smart-toc-widget-service";

export function ServiceMap() {
  const { splitMode, setSplitMode } = useSplitMode();
  const { availableBaseLayers } = useBaseLayer();
  const { map } = useMap();

  const [swipeValue, setSwipeValue] = useState(50);
  const [swipeEnabled, setSwipeEnabled] = useState(false);
  const [mapSize, setMapSize] = useState<[number, number] | null>(null);

  const [leftLayerId, setLeftLayerId] = useState("eMapBasic");
  const [rightLayerId, setRightLayerId] = useState("BM0000000012");

  const apiClient = createGeonAddrgeoClient({
    baseUrl: "https://city.geon.kr/api/",
    crtfckey: "UxizIdSqCePz93ViFt8ghZFFJuOzvUp0",
  });

  const { handleAddressSearch, isLoading } = useAddressSearch({
    apiType: "geon",
    apiClient,
  });
  const { handleRegionSelect, handleRegionList, handlePnuSelect } =
    useRegionSelector({
      apiType: "geon",
      apiClient,
    });

  // 맵 크기 추적
  useEffect(() => {
    if (!map) return;

    const updateMapSize = () => {
      const size = map.getSize();
      if (size) {
        setMapSize(size as [number, number]);
      }
    };

    updateMapSize();
    map.on("change:size", updateMapSize);

    return () => {
      map.un("change:size", updateMapSize);
    };
  }, [map]);

  return (
    <MapContainer className="relative h-full w-full" splitMode={splitMode}>
      <ServiceLayerManager />
      <SmartTOCWidgetService />

      <RegionSelector
        className="absolute left-1/3 top-4 flex items-center gap-2"
        fetchRegionInfo={handleRegionSelect}
        fetchRegionList={handleRegionList}
        fetchPnu={handlePnuSelect}
        useLi={false}
      >
        <SidoSelector />
        <SigunguSelector />
        <EupmyeondongSelector />
        <LiSelector />
      </RegionSelector>

      <AddressSearch
        className="right-4"
        onSearch={handleAddressSearch}
        isLoading={isLoading}
      >
        <div className="flex gap-2">
          <AddressSearchSelect />
          <AddressSearchInput placeholder="검색어를 입력해주세요." />
          <AddressSearchTrigger></AddressSearchTrigger>
        </div>
        <AddressSearchContent>
          <AddressSearchList isLoading={isLoading}>
            <AddressSearchEmpty />
            <AddressSearchItem />
          </AddressSearchList>
        </AddressSearchContent>
      </AddressSearch>

      {/* 메인 툴바 - 포지션에 따라 결정 */}
      <ToolbarContainer
        // posistion 에 따라 popover, tooltip 방향 결정 확인용
        position="center-right"
        // style variant 는 좀 더 확인..
        buttonVariant="separated"
        variant="glass"
      >
        <ToolbarHome tooltip="홈 위치" />

        <ToolbarBasemap>
          <ToolbarBasemapTrigger tooltip="배경지도" />
          <ToolbarBasemapContent />
        </ToolbarBasemap>

        <ToolbarDraw>
          <ToolbarDrawTrigger tooltip="그리기 도구" />
          <ToolbarDrawContent>
            <ToolbarDrawTool mode="point" />
            <ToolbarDrawTool mode="lineString" />
            <ToolbarDrawTool mode="polygon" />
            <ToolbarDrawTool mode="box" />
            <ToolbarDrawTool mode="circle" />
            <ToolbarDrawAction action="stop" />
          </ToolbarDrawContent>
        </ToolbarDraw>

        <ToolbarMeasure>
          <ToolbarMeasureTrigger tooltip="측정 도구" />
          <ToolbarMeasureContent>
            <ToolbarMeasureTool mode="measure-distance" />
            <ToolbarMeasureTool mode="measure-area" />
            <ToolbarMeasureTool mode="measure-round" />
            <ToolbarMeasureTool mode="measure-spot" />
            <ToolbarMeasureAction action="stop" />
          </ToolbarMeasureContent>
        </ToolbarMeasure>

        <ToolbarClear tooltip="모든 그리기 삭제" />

        <Separator orientation="vertical" />

        <ToolbarSplit value={splitMode} onValueChange={setSplitMode}>
          <ToolbarSplitTrigger tooltip="분할지도" />
          <ToolbarSplitContent>
            <ToolbarSplitOption count={1} />
            <ToolbarSplitOption count={2} />
            <ToolbarSplitOption count={3} />
            <ToolbarSplitOption count={4} />
          </ToolbarSplitContent>
        </ToolbarSplit>

        {/* 스와이프. 기본 UI를 어떻게 제공할지에 대한 고민*/}
        <ToolbarSwipe
          value={swipeValue}
          onValueChange={setSwipeValue}
          enabled={swipeEnabled}
          onEnabledChange={setSwipeEnabled}
          leftLayerId={leftLayerId}
          rightLayerId={rightLayerId}
          onLeftLayerChange={setLeftLayerId}
          onRightLayerChange={setRightLayerId}
        >
          <ToolbarSwipeTrigger tooltip="스와이프" />
          <ToolbarSwipeContent>
            {/* 활성화/비활성화 토글 버튼 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSwipeEnabled(!swipeEnabled)}
              className="w-full justify-start"
            >
              <UnfoldHorizontal className="mr-2 h-4 w-4" />
              {swipeEnabled ? "스와이프 비활성화" : "스와이프 활성화"}
            </Button>

            {/* 스와이프 위치 슬라이더 */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">스와이프 위치</span>
                <span className="text-muted-foreground text-xs">
                  {swipeValue}%
                </span>
              </div>
              <Slider
                value={[swipeValue]}
                onValueChange={(values) => setSwipeValue(values[0] ?? 50)}
                min={0}
                max={100}
                step={1}
                className="w-full"
              />
            </div>

            {/* 레이어 선택 */}
            <div className="grid grid-cols-2 gap-3">
              {/* 왼쪽 레이어 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">왼쪽 레이어</label>
                <Select value={leftLayerId} onValueChange={setLeftLayerId}>
                  <SelectTrigger>
                    <SelectValue placeholder="레이어 선택" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableBaseLayers?.map((layer) => (
                      <SelectItem key={layer.id} value={layer.id}>
                        {layer.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* 오른쪽 레이어 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">오른쪽 레이어</label>
                <Select value={rightLayerId} onValueChange={setRightLayerId}>
                  <SelectTrigger>
                    <SelectValue placeholder="레이어 선택" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableBaseLayers?.map((layer) => (
                      <SelectItem key={layer.id} value={layer.id}>
                        {layer.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </ToolbarSwipeContent>
        </ToolbarSwipe>

        <Separator orientation="vertical" />

        <ToolbarDownload>
          <ToolbarDownloadTrigger tooltip="다운로드" />
          <ToolbarDownloadContent>
            <ToolbarDownloadItem downloadType="png" />
            <ToolbarDownloadItem downloadType="pdf" />
          </ToolbarDownloadContent>
        </ToolbarDownload>

        <ToolbarPrint>
          <ToolbarPrintTrigger tooltip="인쇄" />
          <ToolbarPrintContent />
          <ToolbarPrintOriginalDialog />
          <ToolbarPrintPaperDialog />
        </ToolbarPrint>
      </ToolbarContainer>

      {/* 스와이프 드래그 핸들 */}
      <SwipeDragHandle
        value={swipeValue}
        onValueChange={setSwipeValue}
        mapSize={mapSize}
        visible={swipeEnabled}
        className="z-40"
      />

      {/* 줌 컨트롤 - 왼쪽 하단 */}
      <ZoomControl />
    </MapContainer>
  );
}
