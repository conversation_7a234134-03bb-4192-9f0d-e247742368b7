import {
  BaseLayerInfo,
  ControlsProvider,
  MapProvider,
} from "@geon-map/react-odf";
import {
  SidebarInset,
  SidebarProvider,
} from "@geon-ui/react/primitives/sidebar";
import React from "react";

import { getBaseLayers, mergeArrays } from "@/utils/base-layer-utils";

import { InnerSidebar, OuterSidebar, ServiceContextMenu } from "./_components";
import { ServiceLayerProvider } from "./_components/common/service-layer-provider";
import { FacilitySearchProvider } from "./_contexts/facility-search";
import {
  ServiceInnerSidebarProvider,
  ServiceSidebarProvider,
} from "./_contexts/sidebar";

// 기본레이어
const DEFAULT_BASE_LAYERS: BaseLayerInfo[] = [
  {
    id: "eMapBasic",
    name: "바로e맵 기본지도",
    description: "바로e맵 기본 지도 레이어",
    category: "바로e맵",
    layerType: "base",
    layerParams: {
      projection: "EPSG:5179",
      version: "1.0.0",
      format: "image/png",
      request: "GetTile",
      layer: "korean_map",
      style: "korean",
      tileGrid: {
        origin: [-200000, 4000000],
        resolutions: [
          2088.96, 1044.48, 522.24, 261.12, 130.56, 65.28, 32.64, 16.32, 8.16,
          4.08, 2.04, 1.02, 0.51, 0.255, 0.1275, 0.06375,
        ],
        matrixIds: [
          "L05",
          "L06",
          "L07",
          "L08",
          "L09",
          "L10",
          "L11",
          "L12",
          "L13",
          "L14",
          "L15",
          "L16",
          "L17",
          "L18",
          "L19",
          "L20",
        ],
      },
      service: "wmts",
      server: "https://city.geon.kr/api/map/api/map/baroemap",
    },
  },
];

export default async function ServiceLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  let apiBaseLayers: BaseLayerInfo[] = [];
  try {
    apiBaseLayers = await getBaseLayers();
  } catch (error) {
    console.error("❌ Failed to load API base layers in layout:", error);
  }

  // 🔗 전체 레이어 병합 및 타입별 분리
  const allLayers = mergeArrays(DEFAULT_BASE_LAYERS, apiBaseLayers);

  // 🎯 레이어 타입별 분리
  const baseLayers = allLayers.filter((layer) => layer.layerType === "base");
  const overlayLayers = allLayers.filter(
    (layer) => layer.layerType === "overlay" || layer.layerType === "hybrid",
  );

  const defaultActiveLayerId = "eMapBasic";

  return (
    <SidebarProvider>
      <ServiceSidebarProvider>
        <OuterSidebar />
        <FacilitySearchProvider>
          <ServiceInnerSidebarProvider>
            <MapProvider defaultOptions={{ projection: "EPSG:5186" }}>
              <ControlsProvider
                drawOptions={{
                  tools: ["polygon", "point", "lineString"],
                  style: {
                    fill: { color: [255, 0, 0, 0.3] },
                    stroke: { color: [255, 0, 0, 1], width: 2 },
                  },
                }}
                measureOptions={{
                  tools: ["distance", "area"],
                  style: {
                    fill: { color: [0, 255, 0, 0.3] },
                    stroke: { color: [0, 255, 0, 1], width: 2 },
                  },
                }}
                scaleOptions={{ size: 100 }}
              >
                {/* 🆕 선언형 레이어 관리 */}
                <ServiceLayerProvider
                  baseLayers={baseLayers}
                  overlayLayers={overlayLayers}
                  activeBaseLayerId={defaultActiveLayerId}
                />
                <InnerSidebar />
                <SidebarInset className="overflow-hidden">
                  <ServiceContextMenu>{children}</ServiceContextMenu>
                </SidebarInset>
              </ControlsProvider>
            </MapProvider>
          </ServiceInnerSidebarProvider>
        </FacilitySearchProvider>
      </ServiceSidebarProvider>
    </SidebarProvider>
  );
}
