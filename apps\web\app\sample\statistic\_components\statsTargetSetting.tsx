"use client";
import { useMap } from "@geon-map/react-odf";
import { createGeonAddrgeoClient } from "@geon-query/model";
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@geon-ui/react/primitives/accordion";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@geon-ui/react/primitives/select";
import React, { useEffect } from "react";

import { StatiscVo } from "../_type/commVo";
import StatsDataView from "./statsDataView";
import { useStatsContext } from "./statsProvider";

/**
 * @description 행정구역 설정
 */
export default function StatsTargetSetting() {
  const { commVo, setCommVo, commVoClone, commVoApply } = useStatsContext();
  const { map } = useMap();

  console.log(map);
  console.dir(map._mapOption.projection);

  // 최초 컴포넌트 실행시...
  useEffect(() => {
    (async () => {
      const yearList = getYears();
      const regionList = getRegions();
      const rangeList = getRanges();
      const targetList = await apiEmdList();
      const vo = commVoClone();
      vo.yearList = yearList;
      vo.regionList = regionList;
      vo.rangeList = rangeList;
      vo.targetList = targetList?.result || [];
      setCommVo(vo);
    })();
  }, []);

  // 입력대상지역 리스트
  async function apiEmdList() {
    const view = map.getView();
    const viewProps = view.getProperties();
    const epsgSrid = viewProps.projection.code_;
    const srid = epsgSrid
      ? epsgSrid.toUpperCase().replace("EPSG:", "")
      : "5186";

    const apiClient = createGeonAddrgeoClient({
      baseUrl: "https://city.geon.kr/api/",
      crtfckey: "UxizIdSqCePz93ViFt8ghZFFJuOzvUp0",
    });

    const response = await apiClient.administ.emdList({
      sigCd: "46840",
      retGeom: true,
      targetSrid: srid || "5186",
    });
    if (response && response.result) {
      return response;
    } else {
      return { result: [] };
    }
  }

  // 년도 변경
  function getYears() {
    const currentYear = new Date().getFullYear();
    return Array.from({ length: 10 }, (_, index) => currentYear - index);
  }
  function handleYear(unit?: string) {
    const vo = commVoClone();
    vo.yearUnit = unit;
    vo.regionUnit = "0";
    vo.rangeUnit = "0";
    setCommVo(vo);
  }

  // TODO: 지역 변경
  function getRegions(): { val: string; name: string }[] {
    const list: { val: string; name: string }[] = [];
    list.push({ val: "0", name: "선택" });
    list.push({ val: "1", name: "시도" });
    list.push({ val: "2", name: "시군구" });
    list.push({ val: "3", name: "읍면동" });
    return list;
  }
  function handleRegion(unit?: string) {
    const vo = commVoClone();
    vo.regionUnit = unit;
    vo.rangeUnit = "0";
    setCommVo(vo);
  }

  // TODO: 범위 변경
  function getRanges(): { val: string; name: string }[] {
    const list: { val: string; name: string }[] = [];
    list.push({ val: "0", name: "선택" });
    list.push({ val: "1", name: "경계1" });
    list.push({ val: "2", name: "경계2" });
    list.push({ val: "3", name: "경계3" });
    return list;
  }
  async function handleRange(unit: string) {
    const targetList = await apiEmdList();
    const vo = commVoClone();
    vo.rangeUnit = unit;
    vo.targetList = targetList;
    setCommVo(vo);
  }

  // 입력 대상 지역
  function handleTargetSelect(itemT: any) {
    const vo = commVoClone();
    const { dataList } = vo;

    if (!itemT.cd || !itemT.korNm || !itemT.geom) return;

    const statsItem = dataList.filter((item: StatiscVo) => {
      return item?.code == itemT?.cd;
    });

    if (statsItem.length != 0) {
      vo.dataList = dataList.filter((item: StatiscVo) => {
        return item?.code != itemT?.cd;
      });
    } else {
      const voTemplate: StatiscVo = {
        code: itemT?.cd,
        codeName: itemT?.korNm,
        stsVal: 0,
        subVal: 0,
        calVal: 0,
        color: "#ffffff",
        geom: itemT?.geom,
      };

      dataList.push(voTemplate);
      vo.dataList = dataList;
    }
    commVoApply(vo);
  }

  return (
    <AccordionItem value="item_step1">
      <AccordionTrigger className="bg-gray-300 px-4 py-2 text-black">
        행정구역
      </AccordionTrigger>
      <AccordionContent>
        <div>
          <div>
            <div className="mb-2 mt-2 bg-gray-200">▣ 행정구역 단위</div>
            <div className="flex items-center space-x-4">
              <div className="flex flex-col">
                <Select onValueChange={handleYear} value={commVo.yearUnit}>
                  <SelectTrigger>
                    <SelectValue placeholder="년도" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>년도</SelectLabel>
                      {commVo.yearList?.map((item: number, i: number) => (
                        <SelectItem key={`year_${i}`} value={`${item}`}>
                          {item}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex flex-col">
                <Select onValueChange={handleRegion} value={commVo.regionUnit}>
                  <SelectTrigger>
                    <SelectValue placeholder="읍면동" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>읍면동</SelectLabel>
                      {commVo.regionList?.map((item: any, i: number) => (
                        <SelectItem key={`region_${i}`} value={item.val}>
                          {item.name}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex flex-col">
                <Select onValueChange={handleRange} value={commVo.rangeUnit}>
                  <SelectTrigger>
                    <SelectValue placeholder="경계" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>경계</SelectLabel>
                      {commVo.rangeList.map((item: any, i: number) => (
                        <SelectItem key={`range_${i}`} value={item.val}>
                          {item.name}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* 입력 대상 지역 */}
          <div>
            <div className="mb-2 mt-2 border-gray-300 bg-gray-200 font-semibold">
              ▣ 입력 대상 지역
            </div>
            <div className="mb-5 flex flex-col">
              <ul className="h-40 overflow-y-auto rounded bg-gray-100 p-2">
                {commVo.targetList.map((item: any, i: number) => {
                  const dataVo = commVo.dataList.filter(
                    (data: StatiscVo) => data.code === item.cd,
                  );

                  const selectColor =
                    dataVo?.length != 0 ? "bg-gray-300" : "bg-white";

                  return (
                    <li
                      key={i}
                      className={`mb-1 flex h-5 cursor-pointer items-center rounded ${selectColor} shadow-m p-2`}
                      onClick={() => {
                        handleTargetSelect(item);
                      }}
                    >
                      {item?.korNm}
                    </li>
                  );
                })}
              </ul>
            </div>
          </div>
        </div>
        <StatsDataView />
      </AccordionContent>
    </AccordionItem>
  );
}
