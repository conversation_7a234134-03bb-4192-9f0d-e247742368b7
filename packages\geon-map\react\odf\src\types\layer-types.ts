import { CommonStyleSpec } from "@geon-map/core";
import type { ReactNode } from "react";

export interface RenderOptions {
  style?: any; // odfFlatStyle | odfStyleRule | odfWebGLVectorTileStyle
}

// 공통 레이어 속성
interface BaseLayerProps {
  id?: string;
  type: LayerType;
  renderOptions?: RenderOptions;
  className?: string;
  webGLRender?: boolean;
  attributions?: string[];
  children?: ReactNode;
  visible?: boolean;
  zIndex?: number;
  service?: LayerService;
}

// 서비스 타입 정의
export type LayerService =
  | "wms"
  | "wfs"
  | "wmts"
  | "group"
  | "cluster"
  | "hotspot"
  | "heatmap"
  | "aggregate";

// Empty 레이어 옵션 - 파라미터가 필요 없는 빈 레이어
export interface EmptyLayerProps extends BaseLayerProps {
  type: "empty";
  // 추가 파라미터가 필요하지 않음
}

// Geoserver 레이어 옵션
export interface GeoserverLayerProps extends BaseLayerProps {
  type: "geoserver";
  server:
    | string
    | {
        url: string;
        version?: string;
        proxyURL?: string;
        proxyParam?: string;
      };
  info?: {
    lyrId: string;
    lyrNm: string;
  };
  layer: string;
  service: LayerService;
  limit?: number;
  crtfckey?: string;
  bbox?: boolean;
  method?: "get" | "post";
  tiled?: boolean;
  projection?: string;
  geometryType?: string;
  serviceTy?: string;
  name?: string;
  visible?: boolean;
  zIndex?: number;
}

// GeoTIFF 레이어 옵션
export interface GeoTiffLayerProps extends BaseLayerProps {
  type: "geotiff";
  sources: Array<{
    url?: string;
    overviews?: string[];
    proxyURL?: string;
    proxyParam?: string;
    blob?: Blob;
    bands?: number[];
    min?: number;
    max?: number;
    nodata?: number;
  }>;
  normalize?: boolean;
  wrapX?: boolean;
  opaque?: boolean;
  transition?: number;
}

// GeoJSON 레이어 옵션
export interface GeoJSONLayerProps extends BaseLayerProps {
  type: "geojson";
  data: any;
  service: LayerService;
  dataProjectionCode: string;
  featureProjectionCode: string;
}

// KML 레이어 옵션
export interface KMLLayerProps extends BaseLayerProps {
  type: "kml";
  data: string;
  dataProjectionCode: string;
  featureProjectionCode: string;
}

// CSV 레이어 옵션
export interface CSVLayerProps extends BaseLayerProps {
  type: "csv";
  data: string;
  dataProjectionCode: string;
  featureProjectionCode: string;
  geometryColumnName: string;
  delimiter?: string;
}

// API 레이어 옵션
export interface APILayerProps extends BaseLayerProps {
  type: "api";
  layer: string;
  server:
    | string
    | {
        url: string;
        proxyURL?: string;
        proxyParam?: string;
      };
  service: "wms" | "wfs" | "wmts";
  bbox?: boolean;
  tiled?: boolean;
  tileGrid?: {
    origin: number[];
    resolutions: number[];
    matrixIds: string[] | number[];
  };
  projection: string;
  originalOption?: {
    REQUEST?: boolean;
    SERVICE?: boolean;
    BBOX?: boolean | string;
    LAYER_PROJECTION?: string;
    TILEROW_TILECOL_INVERTED_STATE?: boolean;
    WIDTH?: boolean;
    HEIGHT?: boolean;
    VERSION?: boolean;
    TRANSPARENT?: boolean;
    STYLES?: boolean;
    CRS?: boolean;
  };
  parameterFilter?: (params: any) => any;
}

// 모든 레이어 타입을 하나로 통합
export type LayerProps = (
  | GeoserverLayerProps
  | GeoTiffLayerProps
  | GeoJSONLayerProps
  | KMLLayerProps
  | CSVLayerProps
  | APILayerProps
  | SVGLayerProps
  | EmptyLayerProps
  | BaseLayerLayerProps
) & {
  fit?: boolean;
  visible?: boolean;
};

export type LayerType =
  | "geoserver"
  | "geotiff"
  | "geojson"
  | "kml"
  | "csv"
  | "api"
  | "svg"
  | "empty"
  | "geoImage"
  | "draw" // 🆕 그리기 레이어
  | "measure" // 🆕 측정 레이어
  | "clear" // 🆕 정리 레이어
  | "baselayer"; // 🆕 배경 레이어

// Add SVG layer props interface
export interface SVGLayerProps extends BaseLayerProps {
  type: "svg";
  svgContainer: any; // You might want to specify a more specific type depending on your SVG container
  extent: number[]; // [minx, miny, maxx, maxy]
}

export interface LayerStyle {
  type: string;
  properties: Record<string, unknown>;
}

export interface LayerInfo {
  lyrId: string;
  lyrNm: string;
  description?: string;
  metadata?: Record<string, unknown>;
}

export interface LayerConfig {
  type: LayerType;
  params: {
    service?: "wms" | "wfs";
    url?: string;
    layers?: string;
    [key: string]: unknown;
  };
}

export interface Layer {
  id: string;
  name: string;
  type: LayerType;
  visible: boolean;
  zIndex: number;
  odfLayer: import("@geon-map/core").OdfLayer; // ODF Layer 인스턴스
  abstractLayer?: import("@geon-map/core").AbstractLayer; // AbstractLayer 인스턴스 (Core 추상화)
  params: {
    service?: LayerService;
    [key: string]: any;
  };
  info?: LayerInfo;
  style?: LayerStyle;
  // CommonStyleSpec : 공통 스타일 명세 객체
  // - WMS, WFS, GeoJSON 등 소스별 스타일을 공통 구조로 변환해 저장
  // - Legend, 스타일 편집기 등 UI 컴포넌트가 소스 타입과 무관하게 사용 가능
  commonStyleSpec?: CommonStyleSpec;
  filter?: string;
  opacity?: number;
  group?: string;
  children?: Layer[];

  // 🆕 배경 레이어 전용 필드
  isBaseLayer?: boolean;
  baseLayerInfo?: BaseLayerInfo;
}

/**
 * ODF 표준 메서드를 포함한 확장된 Layer 인터페이스
 * 그리기/측정 레이어에서 내보내기/가져오기 기능을 위한 표준 메서드들
 */
export interface LayerWithODFMethods extends Layer {
  // === ODF 표준 피처 관리 메서드 ===
  /** 피처 추가 */
  addFeature(feature: any): boolean;

  /** 여러 피처 추가 */
  addFeatures(features: any[]): boolean;

  /** 피처 제거 */
  removeFeature(feature: any): boolean;

  /** ID로 피처 제거 */
  removeFeatureById(featureId: string | number): boolean;

  /** 모든 피처 조회 */
  getFeatures(): any[];

  /** ID로 피처 조회 */
  getFeatureById(featureId: string | number): any | null;

  /** 모든 피처 제거 */
  clearFeatures(): boolean;

  /** 레이어 초기화 (피처 + 스타일 등 모든 상태) */
  clear(): boolean;

  // === ODF 표준 내보내기/가져오기 메서드 ===
  /** KML로 내보내기 */
  toKML(downloadFile?: boolean): string | null;

  /** GeoJSON으로 내보내기 */
  toGeoJson(): any | null;

  /** KML에서 가져오기 */
  fromKML(
    kml: string,
    dataProjectionCode?: string,
    featureProjectionCode?: string,
  ): boolean;

  /** GeoJSON에서 가져오기 */
  fromGeoJson(
    geoJson: any,
    dataProjectionCode?: string,
    featureProjectionCode?: string,
  ): boolean;
}

export interface LayerContextState {
  layers: Layer[];
  selectedLayerId?: string;
  expandedGroups: Set<string>;
}

export type LayerAction =
  | { type: "ADD_LAYER"; payload: Layer }
  | { type: "REMOVE_LAYER"; payload: string }
  | { type: "UPDATE_LAYER"; payload: { id: string; updates: Partial<Layer> } }
  | { type: "SET_LAYERS"; payload: Layer[] }
  | { type: "TOGGLE_LAYER_VISIBILITY"; payload: string }
  | { type: "SET_LAYER_FILTER"; payload: { id: string; filter: string } }
  | { type: "SET_SELECTED_LAYER"; payload: string }
  | { type: "TOGGLE_GROUP"; payload: string };

export interface AddFeatureOptions {
  /** 피쳐 좌표계 SRID */
  srid?: string;
}

// 🆕 배경 레이어 분류 타입 정의
export type BaseLayerClassType = "base" | "overlay" | "hybrid";

// 🆕 배경 레이어 정보 인터페이스 (확장됨)
export interface BaseLayerInfo {
  id: string;
  name: string;
  description?: string;
  category?: string; // '바로e맵', 'vWorld', '사용자정의' 등
  thumbnail?: string; // base64 이미지

  // 🆕 새로운 필드들 - 하이브리드/중첩 레이어 지원
  layerType: BaseLayerClassType; // 레이어 분류 타입 ('base' | 'overlay' | 'hybrid')

  // API 레이어 파라미터와 동일한 구조 사용
  layerParams: {
    service: LayerService;
    server: string;
    layer?: string;
    [key: string]: any;
  };
}

// 🆕 배경 레이어 Props
export interface BaseLayerLayerProps extends BaseLayerProps {
  type: "baselayer";
  baseLayerInfo: BaseLayerInfo;
  isActive?: boolean;
}
