import { AbstractLayer, GeoJsonLayerConfig } from "../layer-types";

export class GeoJsonLayer extends AbstractLayer {
  protected config: GeoJsonLayerConfig;

  constructor(odfLayer: any, config: GeoJsonLayerConfig) {
    super(odfLayer, config);
    this.config = config;
  }

  setVisible(visible: boolean): void {
    this.safeExecute(
      () => this.odfLayer.setVisible(visible),
      `Failed to set visibility to ${visible}`,
    );
  }

  setOpacity(opacity: number): void {
    this.safeExecute(
      () => this.odfLayer.setOpacity(opacity),
      `Failed to set opacity to ${opacity}`,
    );
  }

  setZIndex(zIndex: number): void {
    this.safeExecute(
      () => this.odfLayer.setZIndex(zIndex),
      `Failed to set zIndex to ${zIndex}`,
    );
  }

  updateFilter(filter: string): void {
    this.safeExecute(() => {
      // GeoJSON 레이어는 다른 방식으로 필터링
      if (this.odfLayer.setFilter) {
        this.odfLayer.setFilter(filter);
      } else if (this.odfLayer.getSource) {
        // OpenLayers 기반인 경우
        const source = this.odfLayer.getSource();
        // 필터 로직 구현 필요
      }
    }, `Failed to update filter: ${filter}`);
  }

  updateStyle(style: any): void {
    this.safeExecute(() => {
      const parsedStyle = typeof style === "string" ? JSON.parse(style) : style;

      if (this.odfLayer.setStyle) {
        // 일반 스타일 적용
        const odfStyle = (globalThis as any).odf?.StyleFactory?.produce?.(
          parsedStyle,
        );
        if (odfStyle) {
          this.odfLayer.setStyle(odfStyle);
        }
      }
    }, `Failed to update style`);
  }

  fit(duration: number = 0): void {
    this.safeExecute(() => this.odfLayer.fit(duration), `Failed to fit layer`);
  }

  // GeoJSON 특화 메서드들
  addFeature(feature: any): boolean {
    return (
      this.safeExecute(() => {
        if (this.odfLayer.addFeature) {
          this.odfLayer.addFeature(feature);
          return true;
        }
        return false;
      }, "Failed to add feature") ?? false
    );
  }

  addFeatures(features: any[]): boolean {
    return (
      this.safeExecute(() => {
        if (this.odfLayer.addFeatures) {
          this.odfLayer.addFeatures(features);
        } else if (this.odfLayer.addFeature) {
          features.forEach((feature) => this.odfLayer.addFeature(feature));
        } else {
          return false;
        }
        return true;
      }, "Failed to add features") ?? false
    );
  }

  removeFeature(feature: any): boolean {
    return (
      this.safeExecute(() => {
        if (this.odfLayer.removeFeature) {
          this.odfLayer.removeFeature(feature);
          return true;
        }
        return false;
      }, "Failed to remove feature") ?? false
    );
  }

  clearFeatures(): void {
    this.safeExecute(() => {
      if (this.odfLayer.clearFeatures) {
        this.odfLayer.clearFeatures();
      }
    }, "Failed to clear features");
  }

  getFeatures(): any[] {
    return (
      this.safeExecute(() => {
        if (this.odfLayer.getFeatures) {
          return this.odfLayer.getFeatures();
        }
        return [];
      }, "Failed to get features") ?? []
    );
  }

  getData(): any {
    return this.config.data;
  }

  // AbstractLayer 메서드 구현
  getOpacity(): number | null {
    try {
      return this.odfLayer.getOpacity?.() ?? null;
    } catch (error) {
      console.error(`[GeoJsonLayer] Failed to get opacity:`, error);
      return null;
    }
  }

  getLegendUrl(options?: any): string | null {
    // GeoJSON 레이어는 일반적으로 범례 URL을 지원하지 않음
    console.warn(`[GeoJsonLayer] Legend URL not supported for GeoJSON layers`);
    return null;
  }
}
