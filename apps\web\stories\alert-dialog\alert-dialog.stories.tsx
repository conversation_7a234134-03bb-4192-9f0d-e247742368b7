import { AlertDialog } from "@geon-ui/react/primitives/alert-dialog";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import AlertDialogDemoSource from "!!raw-loader!./demo";

import { AlertDialogDemo } from "./demo";

const meta = {
  title: "Shadcn/AlertDialog",
  component: AlertDialog,
  tags: ["autodocs"],
  args: {},
  argTypes: {},
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof AlertDialog>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  args: {},
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: AlertDialogDemoSource,
      },
    },
  },
  render: () => <AlertDialogDemo />,
};
