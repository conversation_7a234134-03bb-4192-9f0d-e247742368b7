"use client";

import { useLayer } from "@geon-map/react-odf";
import { useEffect, useMemo, useRef, useState } from "react";

import type { LayerProps, LayerState } from "./layer.types";

/**
 * 선언적 API로 지도 레이어를 관리하는 컴포넌트
 *
 * @example
 * ```tsx
 * <Layer
 *   id="water-pipe"
 *   config={{
 *     type: "geoserver",
 *     server: { url: "https://geoserver.example.com" },
 *     layer: "wtl_pipe_lm",
 *     service: "wms"
 *   }}
 *   visible={true}
 *   opacity={0.8}
 *   cqlFilter="ftrIdn = '241001'"
 *   onLayerReady={(layerId) => console.log('Layer ready:', layerId)}
 * />
 * ```
 */
export function Layer({
  id,
  config,
  visible = true,
  opacity = 1,
  cqlFilter,
  zIndex = 1,
  onLayerReady,
  onLayerError,
}: LayerProps) {
  const {
    addLayer,
    removeLayer,
    setVisible,
    setOpacity,
    setZIndex,
    findLayer,
  } = useLayer();

  // 레이어 상태 관리
  const [state, setState] = useState<LayerState>({
    mapLayerId: null,
    loading: false,
    error: null,
  });

  // 이전 값들을 추적하기 위한 ref
  const prevConfigRef = useRef<string>("");
  const prevCqlFilterRef = useRef<string | null>(null);

  // 🚀 메모이제이션된 config 키 생성
  const configKey = useMemo(() => JSON.stringify(config), [config]);
  const configChanged = prevConfigRef.current !== configKey;
  const cqlFilterChanged = prevCqlFilterRef.current !== cqlFilter;

  // 🚀 메모이제이션된 레이어 설정 (중복 생성 방지)
  const layerConfig = useMemo(
    () => ({
      ...config,
      visible,
      ...(cqlFilter && { cqlFilter }),
    }),
    [config, visible, cqlFilter],
  );

  // 컴포넌트 마운트 시에만 로그
  const isFirstMount = !prevConfigRef.current;
  if (isFirstMount) {
    console.log(`[Layer:${id}] 🔄 Component Mounted`);
  }

  // 레이어 생성/업데이트 효과
  useEffect(() => {
    let isCancelled = false;

    const createOrUpdateLayer = async () => {
      // 🚀 설정이 변경되었거나 레이어가 없는 경우만 레이어 재생성 (CQL 필터 변경 제외)
      if (configChanged || !state.mapLayerId) {
        setState((prev) => ({ ...prev, loading: true, error: null }));

        try {
          // 기존 레이어가 있으면 제거
          if (state.mapLayerId) {
            removeLayer(state.mapLayerId);
          }

          // 🚀 메모이제이션된 레이어 설정 사용 (중복 생성 방지)
          const newLayerId = await addLayer(layerConfig);

          if (isCancelled) return;

          // addLayer가 undefined를 반환하는 경우 처리 (정상 상황일 수 있음)
          if (!newLayerId) {
            console.warn(
              `[Layer:${id}] ⚠️ addLayer returned falsy result (possibly duplicate or loading)`,
              {
                layerConfig,
                newLayerId,
              },
            );

            // 로딩 상태 해제하고 조용히 종료 (에러가 아님)
            setState((prev) => ({
              ...prev,
              loading: false,
              error: null,
            }));
            return;
          }

          setState((prev) => ({
            ...prev,
            mapLayerId: newLayerId,
            loading: false,
            error: null,
          }));

          // 레이어 준비 완료 콜백 호출
          onLayerReady?.(newLayerId);
        } catch (error) {
          if (isCancelled) return;

          console.error(`[Layer:${id}] ❌ Failed to create layer:`, error);
          const layerError =
            error instanceof Error ? error : new Error(String(error));

          setState((prev) => ({
            ...prev,
            loading: false,
            error: layerError,
            mapLayerId: null,
          }));

          onLayerError?.(layerError);
        }
      }
    };

    createOrUpdateLayer();

    // 이전 값들 업데이트
    prevConfigRef.current = configKey;
    prevCqlFilterRef.current = cqlFilter || null;

    return () => {
      isCancelled = true;
    };
  }, [id, configChanged, state.mapLayerId, layerConfig]);

  // 가시성
  useEffect(() => {
    if (state.mapLayerId && !state.loading) {
      setVisible(state.mapLayerId, visible);
    }
  }, [id, state.mapLayerId, state.loading, visible, setVisible]);

  // 투명도
  useEffect(() => {
    if (state.mapLayerId && !state.loading) {
      setOpacity(state.mapLayerId, opacity);
    }
  }, [id, state.mapLayerId, state.loading, opacity, setOpacity]);

  // Z-Index (레이어 순서) - 명시적으로 전달된 경우에만 처리
  useEffect(() => {
    if (state.mapLayerId && !state.loading && typeof zIndex === "number") {
      console.log(`[Layer:${id}] 🔢 Setting z-index: ${zIndex}`);
      setZIndex(state.mapLayerId, zIndex);
    }
  }, [id, state.mapLayerId, state.loading, zIndex, setZIndex]);

  // 필터
  useEffect(() => {
    if (state.mapLayerId && !state.loading && cqlFilterChanged) {
      if (state.mapLayerId) {
        try {
          // AbstractLayer를 통해 CQL 필터 업데이트
          const layer = findLayer(state.mapLayerId);
          if (layer?.abstractLayer) {
            layer.abstractLayer.updateFilter(cqlFilter || "");
          } else {
            // fallback: LayerFactory 정적 메서드 사용
            const odfLayer = layer?.odfLayer;
            if (odfLayer) {
              (globalThis as any).LayerFactory?.setLayerFilter?.(
                odfLayer,
                cqlFilter || "",
              );
            }
          }
        } catch (error) {
          console.error(`[Layer:${id}] ❌ Failed to update CQL filter:`, error);
        }
      }
    }
  }, [
    id,
    state.mapLayerId,
    state.loading,
    cqlFilter,
    cqlFilterChanged,
    findLayer,
  ]);

  // 컴포넌트 언마운트 시 레이어 정리
  useEffect(() => {
    return () => {
      if (state.mapLayerId) {
        console.log(
          `[Layer:${id}] 🧹 Component unmounting, cleaning up layer: ${state.mapLayerId}`,
        );
        try {
          removeLayer(state.mapLayerId);
        } catch (error) {
          console.error(`[Layer:${id}] ❌ Failed to cleanup layer:`, error);
        }
      }
    };
  }, [id, state.mapLayerId, removeLayer]);

  // 이 컴포넌트는 UI를 렌더링하지 않음 (레이어 관리만)
  return null;
}
