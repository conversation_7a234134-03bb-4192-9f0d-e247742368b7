import { useLayer, useMap } from "@geon-map/react-odf";
import { createContext, ReactNode, useContext, useState } from "react";

import { CommVo, StatiscVo } from "../_type/commVo";
import { commUtils } from "../_utils/commUtils";

// 통계 데이터 Context 생성
export const StstisticContext = createContext<any>(undefined);

export const useStatsContext = () => {
  const context = useContext(StstisticContext);
  if (!context) {
    throw new Error("error useStatsContext");
  }
  return context;
};

/**
 * @description 통계지도
 */
export const StatsProvider = ({ children }: { children: ReactNode }) => {
  // 초기 CommVo 객체 정의 (기본값 설정)
  const _commVo: CommVo = {
    yearList: [],
    yearUnit: "", // 선택된 년도
    regionList: [],
    regionUnit: "", // 선택된 지역 (시, 시도, 읍면동)
    rangeList: [],
    rangeUnit: "", // 선택된 영역 범위
    targetList: [], // 입력 대상 지역
    stsUnit: "", // 선택된 수치 단위
    sumYn: "Y", // 합계 표시 여부
    subValYn: "Y", // 보조통계 사용 여부
    subPresetYn: "Y", // 프리셋 사용 여부
    subPresetVal: "", // 선택된 프리셋 값
    subPresetList: [], // 프리셋 리스트
    subPresetDataList: [], // 프리셋 데이터
    subPresetCalUnit: "1", // 프리셋 단위조정 값
    calFormula: "T0", // 계산 공식 (T1: A/B, T2: A/B * 100, T3: A/D * 100)
    outLineColor: "#000000", // 행정구역 경계 색상
    outLineSize: 3, // 경계 선 두께
    outLineTrans: 100, // 경계 선 투명도
    isLabelAdm: "Y", // 지역명 라벨 표시 여부
    isLabelCal: "Y", // 통계값 라벨 표시 여부
    textFontWrit: "Dotum", // 폰트 스타일
    textFontSize: "12", // 폰트 크기
    textFontColor: "#000000", // 폰트 색상
    textLabelLevel: "0", // 라벨 축적 수준
    statsType: "EqInterval", // 통계 범위 타입 (기본값: EqInterval)
    statsRangeSize: "10", // 통계 범위 크기
    statsColorTrans: 100, // 색상 투명도
    statsColor: "red", // 색상
    statsColorList: [], // 색상 리스트
    dataList: [], // 행정구역별 통계값 데이터셋
    stsTotalVal: 0, // 통계값 합
    subTotalVal: 0, // 보조통계값 합
    calTotalVal: 0, // 계산값 합

    statsLayerId: "", // 레이어 아이디
    statsLayerStyle: "", // 레이어 스타일 정보
    statsLayerNiceDetail: null, // 레이어 발행 정보
  };

  // commVo 상태 정의
  const [commVo, setCommVo] = useState(_commVo);
  const { odf } = useMap();
  const { addLayer, getLayerById, addFeatures, clearFeatures } = useLayer();

  // commVo의 복사본을 반환하는 함수
  function commVoClone() {
    return JSON.parse(JSON.stringify(commVo));
  }

  // 적용
  function commVoApply(vo: CommVo) {
    calculation(vo);
    changeColor(vo);
    statsLayer(vo);
    setCommVo(vo);
  }

  // 통계 레이어 생성
  async function statsLayer(vo: CommVo) {
    // 레이어 발행 알림 정보 초기화
    vo.statsLayerNiceDetail = null;

    // 레이어 생성
    let layerId = vo.statsLayerId;
    if (vo.dataList.length === 0) return;
    if (layerId) {
      clearFeatures(layerId);
    } else {
      layerId = (await addLayer({ type: "empty" })) as string;
      vo.statsLayerId = layerId;
    }
    const layer = getLayerById(layerId);
    const features = geoJsonToFeatures(vo.dataList);
    const customStyle = getCustomStyle(vo);
    vo.statsLayerStyle = customStyle.getJSON();
    addFeatures(layerId, features);
    if (layer) layer.odfLayer.setStyle(customStyle);
  }

  // wkt 를 피처 객체 만들기
  function geoJsonToFeatures(datas: StatiscVo[]): any[] {
    return datas.map((item: StatiscVo) => geoJsonToFeature(item));
  }
  function geoJsonToFeature(item: StatiscVo): any {
    if (!item.code || !item.codeName || !item.geom) return null;
    const feature = odf.FeatureFactory.fromWKT(item.geom, {
      code: item.code,
      codeName: item.codeName,
      stsVal: item.stsVal,
      subVal: item.subVal || 0,
      calVal: item.calVal || 0,
      color: item.color || "#FFFFFF",
    });
    feature.setId(item.code);
    return feature;
  }

  // 레이어 스타일 만들기
  function getCustomStyle(properties: CommVo): any {
    const defaultOptions = {
      fill: {
        color: "#FFFFFF",
      },
      stroke: {
        color: "#000000",
        width: 1,
      },
      text: {
        text: "{{codeName}}\n{{calVal}}",
        font: "bold 12px Gulim",
        fill: {
          color: "#000000",
        },
        overflow: true,
        scale: 1,
      },
      geometry: function (feature: any) {
        const geom = feature.getGeometry();
        const geomType = feature.getGeometry().getType();
        if (geomType === "MultiPolygon") {
          const polys = geom.getPolygons().sort(function (a: any, b: any) {
            const areaA = a.getArea();
            const areaB = b.getArea();
            return areaA > areaB ? 1 : areaA < areaB ? -1 : 0;
          });
          return polys.pop();
        }
        return geom;
      },
    };

    if (properties) {
      // 경계 설정
      const outLineTrans = properties.outLineTrans
        ? properties.outLineTrans / 100
        : 1;

      defaultOptions.stroke.color = properties.outLineColor
        ? properties.outLineColor +
          commUtils.toHex(Math.round(outLineTrans * 255))
        : "#000000";

      defaultOptions.stroke.width = properties.outLineSize
        ? properties.outLineSize
        : 1;

      // 라벨 설정
      const label = [];
      if (properties.isLabelAdm == "Y") label.push(`{{codeName}}`);
      if (properties.isLabelCal == "Y") label.push(`{{calVal}}`);

      const font = properties.textFontWrit ? properties.textFontWrit : "Gulim";
      const fontSize = properties.textFontSize ? properties.textFontSize : "12";
      const fontColor = properties.textFontColor
        ? properties.textFontColor
        : "#000000";
      defaultOptions.text.font = `bold ${fontSize}px ${font}`;
      defaultOptions.text.fill.color = fontColor;
      defaultOptions.text.text = label.join("\n");
    }

    return odf.StyleFactory.produceFunction([
      {
        seperatorFunc: "default",
        style: defaultOptions,
        callbackFunc: function (style: any, feature: any, resolution: any) {
          const properties = feature.getProperties();
          const color = properties.color;
          const codeName = properties.codeName;
          const _resolution =
            Number(properties.textLabelLevel) > 0
              ? Number(properties.textLabelLevel)
              : 2000000;

          if (style) {
            const fillColor = color || "#FFFFFF";
            style.getFill().setColor(fillColor);
            if (resolution < _resolution) {
              let text = style.getText().getText();
              if (text) {
                const value = commUtils.toFloat(properties.calVal);
                const calVal = commUtils.getCommaVal(value);
                text = text.replaceAll("{{codeName}}", codeName);
                text = text.replaceAll("{{calVal}}", calVal);
                style.getText().setText(text);
              }
            } else {
              style.getText().setText("");
            }
          }
        },
      },
    ]);
  }

  // 통계값 계산 함수
  function calculation(vo: CommVo) {
    const isValid = (val: number) => isFinite(val) && !isNaN(val);
    const calFormula = vo.calFormula;

    let stsTotalVal = 0;
    let subTotalVal = 0;
    let calTotalVal = 0;
    const dataList = JSON.parse(JSON.stringify(vo.dataList));
    const subPresetCalUnit = isValid(Number(vo.subPresetCalUnit))
      ? Number(vo.subPresetCalUnit)
      : 1;

    if (dataList.length > 0) {
      dataList.forEach((item: StatiscVo) => {
        const stsVal = Number(item.stsVal);
        const subVal = Number(item.subVal) / subPresetCalUnit;
        item.stsVal = commUtils.numberToFixed(stsVal);
        item.subVal = commUtils.numberToFixed(subVal);
        stsTotalVal = commUtils.numberToFixed(stsTotalVal + stsVal);
        subTotalVal = commUtils.numberToFixed(subTotalVal + subVal);
      });

      // 계산 공식을 적용하여 계산값을 구함
      dataList.forEach((item: StatiscVo) => {
        const stsVal = Number(item.stsVal);
        const subVal = Number(item.subVal);
        let updateCalVal = 0;

        switch (calFormula) {
          case "T1": // A/B
            updateCalVal =
              isValid(stsVal) && isValid(subVal) && subVal !== 0
                ? stsVal / subVal
                : 0;
            break;
          case "T2": // A/B * 100
            updateCalVal =
              isValid(stsVal) && isValid(subVal) && subVal !== 0
                ? (stsVal / subVal) * 100
                : 0;
            break;
          case "T3": // A/D * 100
            updateCalVal =
              isValid(stsVal) && isValid(subTotalVal) && subTotalVal !== 0
                ? (stsVal / subTotalVal) * 100
                : 0;
            break;
          default:
            updateCalVal = stsVal;
        }

        item.calVal = commUtils.numberToFixed(updateCalVal);
        calTotalVal += commUtils.numberToFixed(item.calVal);
      });

      // 최종 통계값 및 계산값 설정
      vo.stsTotalVal = stsTotalVal;
      vo.subTotalVal = subTotalVal;
      vo.calTotalVal = calTotalVal;
      vo.dataList = dataList;
    }
  }

  // 색상 변경 함수
  function changeColor(vo: CommVo) {
    const {
      statsType,
      statsRangeSize,
      statsColor,
      statsColorTrans,
      statsColorList,
    } = vo;

    // 급간 데이터결과
    const values = vo.dataList.map((item) => item.calVal);
    const rangeSize = Number(statsRangeSize);
    const stsRange =
      rangeSize > 0
        ? commUtils.getStatsRange(statsType, rangeSize, values)
        : [];

    // 투명도
    const alpha =
      statsColorTrans !== undefined && statsColorTrans !== null
        ? statsColorTrans / 100
        : 1;

    // 색상 리스트
    const colorList = (vo.statsColorList =
      statsColorList.length === 0
        ? commUtils.generateColors(
            statsColor || "red",
            rangeSize || 10,
            1,
            false,
          )
        : statsColorList);

    // 색상 적용
    const endColor = colorList[colorList.length - 1];
    vo.dataList.forEach((item: StatiscVo) => {
      // 사용자 정의 색상 적용
      if (item?.userClr) {
        item.color = item?.userClr;
        return item;
      }
      // 범위에 맞는 색상 적용
      item.color = commUtils.hexInAlpha(endColor?.hex, alpha);
      colorList.forEach((color: { rgb: string[]; hex: string }, i: number) => {
        if (!stsRange) return;
        const start = stsRange[i] as number;
        const end = stsRange[i + 1] as number;
        if (item.calVal >= start && item.calVal <= end) {
          item.color = commUtils.hexInAlpha(color.hex || endColor?.hex, alpha);
        }
      });
    });
  }

  return (
    <StstisticContext.Provider
      value={{ commVo, setCommVo, commVoClone, commVoApply }}
    >
      {children}
    </StstisticContext.Provider>
  );
};
