import type { TOCNode } from "@geon-map/react-ui/types";

export const TOC_DATA: TOCNode[] = [
  {
    id: "common",
    name: "공통 레이어",
    visible: true,
    type: "group",
    expanded: true,
    children: [
      {
        id: "muan_gis:magp_hilight",
        name: "무안군 기본",
        visible: true,
        type: "layer",
      },
      {
        id: "common-layer",
        name: "레이어",
        visible: true,
        type: "group",
        expanded: true,
        children: [
          {
            id: "Wgeonapi:L100003391",
            name: "레이어a",
            visible: true,
            type: "layer",
          },
        ],
      },
      {
        id: "common-administrativeBoundary",
        name: "법정경계",
        visible: true,
        expanded: true,
        type: "group",
        children: [],
      },
    ],
  },
];
