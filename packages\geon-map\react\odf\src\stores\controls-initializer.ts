/**
 * 🎯 Controls 초기화 로직 (Dependency Injection 패턴)
 *
 * Map 초기화 완료 후 Controls를 초기화하고 올바른 Store에 저장합니다.
 * 독립적인 MapInstance와 전역 스토어 모두 지원합니다.
 */

import type { MapStoreContextType } from "../contexts/map-store-context";
import type { ControlsConfig } from "../providers/controls-provider";
import type { BaseLayerInfo, Layer } from "../types/layer-types";
import { getControlsConfig } from "./controls-config-store";
import { CoreInstanceManager } from "./core-instances";
import { useDrawStore } from "./draw-store";
import { useLayerStore } from "./layer-store";

/**
 * Controls 설정을 기반으로 모든 Control을 초기화
 */
export async function initializeControlsFromConfig(
  config: ControlsConfig | null,
  mapStore: any,
  drawStore: ReturnType<typeof useDrawStore.getState>,
  layerStore: ReturnType<typeof useLayerStore.getState>,
): Promise<void> {
  if (!config || !config.autoInitialize) return;

  const { map, odf } = mapStore;
  if (!map || !odf) return;

  const errors: string[] = [];

  try {
    // 1. Draw Control 초기화
    if (config.drawOptions) {
      await initializeDrawControl(
        config.drawOptions,
        map,
        odf,
        drawStore,
        layerStore,
        errors,
      );
    }

    // 2. Measure Control 초기화
    if (config.measureOptions) {
      await initializeMeasureControl(
        config.measureOptions,
        map,
        odf,
        drawStore,
        layerStore,
        errors,
      );
    }

    // 3. Clear Control 초기화
    if (config.clearOptions) {
      await initializeClearControl(
        config.clearOptions,
        map,
        odf,
        drawStore,
        errors,
      );
    }

    // 5. Scale Control 초기화
    if (config.scaleOptions) {
      await initializeScaleControl(
        config.scaleOptions,
        map,
        odf,
        mapStore,
        errors,
      );
    }

    // 6. Overview Control 초기화
    if (config.overviewOptions?.enabled) {
      await initializeOverviewControl(map, odf, mapStore, errors);
    }

    // 에러가 있으면 콜백 호출
    if (errors.length > 0) {
      const error = new Error(`Controls 초기화 실패: ${errors.join(", ")}`);
      console.error("❌ Controls initialization failed:", errors);
      config.onError?.(error);
    }
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    console.error("❌ Failed to initialize controls:", err);
    config.onError?.(err);
  }
}

/**
 * Draw Control 초기화
 */
async function initializeDrawControl(
  drawOptions: any,
  map: any,
  odf: any,
  drawStore: ReturnType<typeof useDrawStore.getState>,
  layerStore: ReturnType<typeof useLayerStore.getState>,
  errors: string[],
): Promise<void> {
  try {
    const { drawCore, errors: drawErrors } = CoreInstanceManager.createDrawCore(
      map,
      odf,
      drawOptions,
    );

    if (drawCore) {
      drawStore.setDrawCore(drawCore);

      // Provider 스타일을 Store와 동기화
      try {
        const style = (drawOptions as any)?.style;
        if (style) {
          drawStore.setDrawStyle(style);
        }
      } catch (e) {
        console.error(e);
      }

      // LayerStore에 Draw 레이어 등록
      const drawLayer = drawCore.getDrawLayer();
      if (drawLayer) {
        layerStore.addDrawLayer(drawLayer, {
          name: "Draw Layer",
          visible: true,
        });
      }
    }

    errors.push(...drawErrors);
  } catch (error) {
    errors.push(`Draw Control 초기화 실패: ${error}`);
  }
}

/**
 * Measure Control 초기화
 */
async function initializeMeasureControl(
  measureOptions: any,
  map: any,
  odf: any,
  drawStore: ReturnType<typeof useDrawStore.getState>,
  layerStore: ReturnType<typeof useLayerStore.getState>,
  errors: string[],
): Promise<void> {
  try {
    const { measureCore, errors: measureErrors } =
      CoreInstanceManager.createMeasureOnlyCores(map, odf, measureOptions);

    if (measureCore) {
      drawStore.setMeasureCore(measureCore);

      // Provider 스타일을 Store와 동기화
      try {
        const style = (measureOptions as any)?.style;
        if (style) {
          drawStore.setMeasureStyle(style);
        }
      } catch (e) {
        console.error(e);
      }

      // LayerStore에 Measure 레이어 등록
      const measureLayer = measureCore.getMeasureLayer();
      if (measureLayer) {
        layerStore.addMeasureLayer(measureLayer, {
          name: "Measure Layer",
          visible: true,
        });
      }
    }

    errors.push(...measureErrors);
  } catch (error) {
    errors.push(`Measure Control 초기화 실패: ${error}`);
  }
}

/**
 * Clear Control 초기화
 */
async function initializeClearControl(
  clearOptions: any,
  map: any,
  odf: any,
  drawStore: ReturnType<typeof useDrawStore.getState>,
  errors: string[],
): Promise<void> {
  try {
    const { clearCore, errors: clearErrors } =
      CoreInstanceManager.createClearOnlyCores(
        map,
        odf,
        clearOptions?.clearAll ?? true,
      );

    if (clearCore) {
      drawStore.setClearCore(clearCore);
    }

    errors.push(...clearErrors);
  } catch (error) {
    errors.push(`Clear Control 초기화 실패: ${error}`);
  }
}

/**
 * Scale Control 초기화
 */
async function initializeScaleControl(
  scaleOptions: any,
  map: any,
  odf: any,
  mapStore: any,
  errors: string[],
): Promise<void> {
  try {
    const { scaleInstance, errors: scaleErrors } =
      CoreInstanceManager.createScaleCores(map, odf, scaleOptions);

    if (scaleInstance) {
      mapStore.setScaleInstance(scaleInstance);
    }

    errors.push(...scaleErrors);
  } catch (error) {
    errors.push(`Scale Control 초기화 실패: ${error}`);
  }
}

/**
 * Overview Control 초기화
 */
async function initializeOverviewControl(
  map: any,
  odf: any,
  mapStore: any,
  errors: string[],
): Promise<void> {
  try {
    const { overviewCore, errors: overviewErrors } =
      CoreInstanceManager.createOverviewCore(map, odf);

    if (overviewCore) {
      mapStore.setOverviewInstance(overviewCore);
    }

    errors.push(...overviewErrors);
  } catch (error) {
    errors.push(`Overview Control 초기화 실패: ${error}`);
  }
}

/**
 * 🎯 독립적인 MapInstance를 위한 기본 Controls 초기화
 *
 * Config 없이도 기본적인 Controls를 초기화합니다.
 * split-map과 같은 다중 지도 인스턴스에서 사용됩니다.
 */
export async function initializeBasicControlsForInstance(
  mapInstance: MapStoreContextType,
): Promise<void> {
  const { mapStore, drawStore, layerStore } = mapInstance;
  const { map, odf } = mapStore.getState();

  if (!map || !odf) {
    console.warn("⚠️ Map 또는 ODF가 초기화되지 않았습니다.");
    return;
  }

  const errors: string[] = [];

  try {
    // 1. Draw Control 기본 초기화
    const { drawCore, errors: drawErrors } = CoreInstanceManager.createDrawCore(
      map,
      odf,
      { geometryType: "all" }, // 기본 옵션
    );

    if (drawCore) {
      drawStore.getState().setDrawCore(drawCore);

      // LayerStore에 Draw 레이어 등록
      const drawLayer = drawCore.getDrawLayer();
      if (drawLayer) {
        layerStore.getState().addDrawLayer(drawLayer, {
          name: "Draw Layer",
          visible: true,
        });
      }
    }
    errors.push(...drawErrors);

    // 2. Measure Control 기본 초기화
    const { measureCore, errors: measureErrors } =
      CoreInstanceManager.createMeasureOnlyCores(map, odf, {});

    if (measureCore) {
      drawStore.getState().setMeasureCore(measureCore);

      // LayerStore에 Measure 레이어 등록
      const measureLayer = measureCore.getMeasureLayer();
      if (measureLayer) {
        layerStore.getState().addMeasureLayer(measureLayer, {
          name: "Measure Layer",
          visible: true,
        });
      }
    }
    errors.push(...measureErrors);

    // 3. Clear Control 기본 초기화
    const { clearCore, errors: clearErrors } =
      CoreInstanceManager.createClearOnlyCores(map, odf, true);

    if (clearCore) {
      drawStore.getState().setClearCore(clearCore);
    }
    errors.push(...clearErrors);

    // 🆕 5. 새로운 BaseLayer 시스템 초기화 (ControlsProvider 설정 기반)
    await initializeBaseLayerSystem(mapInstance);

    console.log(
      `🎯 MapInstance(${mapInstance.mapId}) BaseLayer 시스템 초기화 완료`,
    );

    if (errors.length > 0) {
      console.warn(
        `⚠️ MapInstance(${mapInstance.mapId}) Controls 초기화 중 일부 오류:`,
        errors,
      );
    } else {
      console.log(
        `✅ MapInstance(${mapInstance.mapId}) 기본 Controls 초기화 완료`,
      );
    }
  } catch (error) {
    console.error(
      `❌ MapInstance(${mapInstance.mapId}) Controls 초기화 실패:`,
      error,
    );
  }
}

/**
 * 🆕 새로운 BaseLayer 시스템 초기화
 * ControlsProvider에서 설정된 basemapOptions를 기반으로 배경지도를 초기화합니다.
 */
async function initializeBaseLayerSystem(
  mapInstance: MapStoreContextType,
): Promise<void> {
  const { layerStore } = mapInstance;
  const { map, odf } = mapInstance.mapStore.getState();
  const layerFactory = layerStore.getState().layerFactory;

  if (!map || !odf || !layerFactory) {
    console.warn("⚠️ Map, ODF 또는 LayerFactory가 초기화되지 않았습니다.");
    return;
  }

  console.log(
    `🎯 MapInstance(${mapInstance.mapId}) BaseLayer 시스템 초기화 시작`,
  );

  try {
    // ControlsProvider에서 설정된 baseLayerOptions 가져오기
    const controlsConfig = getControlsConfig();
    const baseLayerOptions = controlsConfig?.baseLayerOptions;

    if (
      !baseLayerOptions?.baseLayers ||
      baseLayerOptions.baseLayers.length === 0
    ) {
      console.log(
        "📋 기본 배경지도 설정이 없습니다. BaseLayer 시스템 초기화를 건너뜁니다.",
      );
      return;
    }

    console.log("🔧 기본 배경지도 설정 발견:", {
      baseLayersCount: baseLayerOptions.baseLayers.length,
      overlayLayersCount: baseLayerOptions.overlayLayers?.length || 0,
      activeLayerId: baseLayerOptions.activeLayerId,
    });

    // 1. 배경지도 옵션들을 LayerStore에 등록
    layerStore.getState().setAvailableBaseLayers(baseLayerOptions.baseLayers);

    // 1-2. 오버레이 레이어 옵션들도 LayerStore에 등록
    if (
      baseLayerOptions.overlayLayers &&
      baseLayerOptions.overlayLayers.length > 0
    ) {
      layerStore
        .getState()
        .setAvailableOverlayLayers(baseLayerOptions.overlayLayers);
    }

    // 2. 기본 활성 배경지도 생성 및 활성화
    if (baseLayerOptions.activeLayerId) {
      const defaultBaseLayer = baseLayerOptions.baseLayers.find(
        (layer: BaseLayerInfo) => layer.id === baseLayerOptions.activeLayerId,
      );

      if (defaultBaseLayer) {
        console.log("🚀 기본 배경지도 생성 중:", defaultBaseLayer.name);

        // ODF 레이어 생성
        const odfLayer = layerFactory.produce(
          "api",
          defaultBaseLayer.layerParams,
        );
        odfLayer.setODFId(baseLayerOptions.activeLayerId);
        odfLayer.setMap(map);

        // Layer 객체 생성
        const newLayer: Layer = {
          id: baseLayerOptions.activeLayerId,
          name: defaultBaseLayer.name,
          type: "baselayer",
          visible: true, // 기본 배경지도는 바로 표시
          zIndex: -1, // 배경 레이어는 가장 아래
          odfLayer,
          isBaseLayer: true,
          baseLayerInfo: defaultBaseLayer,
          params: {
            ...defaultBaseLayer.layerParams,
            service: defaultBaseLayer.layerParams.service as any,
          },
        };

        // LayerStore에 추가
        layerStore.getState().addLayer(newLayer);
        layerStore
          .getState()
          .setCurrentBaseLayer(baseLayerOptions.activeLayerId);

        // Z-Index 설정 (배경 레이어는 가장 아래)
        if (odfLayer.setZIndex) {
          odfLayer.setZIndex(-1);
          console.log(
            `✅ Default base layer z-index set to -1: ${baseLayerOptions.activeLayerId}`,
          );
        } else {
          console.warn(
            `⚠️ Default base layer does not support setZIndex: ${baseLayerOptions.activeLayerId}`,
          );
        }

        console.log("✅ 기본 배경지도 초기화 완료:", defaultBaseLayer.name);
      } else {
        console.warn(
          "⚠️ 기본 활성 배경지도를 찾을 수 없습니다:",
          baseLayerOptions.activeLayerId,
        );
      }
    }
  } catch (error) {
    console.error(
      `❌ MapInstance(${mapInstance.mapId}) BaseLayer 시스템 초기화 실패:`,
      error,
    );
  }
}
