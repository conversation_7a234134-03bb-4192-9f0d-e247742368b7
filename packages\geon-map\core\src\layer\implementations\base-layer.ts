import { AbstractLayer, LayerConfig } from "../layer-types";

/**
 * 기본 레이어 구현체 (empty, svg 등)
 */
export class BaseLayer extends AbstractLayer {
  constructor(odfLayer: any, config: LayerConfig) {
    super(odfLayer, config);
  }

  setVisible(visible: boolean): void {
    this.safeExecute(
      () => this.odfLayer.setVisible(visible),
      `Failed to set visibility to ${visible}`,
    );
  }

  setOpacity(opacity: number): void {
    this.safeExecute(
      () => this.odfLayer.setOpacity(opacity),
      `Failed to set opacity to ${opacity}`,
    );
  }

  setZIndex(zIndex: number): void {
    this.safeExecute(
      () => this.odfLayer.setZIndex(zIndex),
      `Failed to set zIndex to ${zIndex}`,
    );
  }

  updateFilter(filter: string): void {
    // 기본 레이어는 필터링을 지원하지 않음
    console.warn(
      `[${this.constructor.name}] Filter not supported for layer type: ${this.config.type}`,
    );
  }

  updateStyle(style: any): void {
    this.safeExecute(() => {
      const parsedStyle = typeof style === "string" ? JSON.parse(style) : style;

      if (this.odfLayer.setStyle) {
        const odfStyle = (globalThis as any).odf?.StyleFactory?.produce?.(
          parsedStyle,
        );
        if (odfStyle) {
          this.odfLayer.setStyle(odfStyle);
        }
      }
    }, `Failed to update style`);
  }

  fit(duration: number = 0): void {
    this.safeExecute(() => this.odfLayer.fit(duration), `Failed to fit layer`);
  }

  getOpacity(): number | null {
    try {
      return this.odfLayer.getOpacity?.() ?? null;
    } catch (error) {
      console.error(`[${this.constructor.name}] Failed to get opacity:`, error);
      return null;
    }
  }

  clearFeatures(): void {
    this.safeExecute(
      () => this.odfLayer.clearFeatures?.(),
      `Failed to clear features`,
    );
  }

  getLegendUrl(options?: any): string | null {
    try {
      return this.odfLayer.getLegendUrl?.(options) ?? null;
    } catch (error) {
      console.error(
        `[${this.constructor.name}] Failed to get legend URL:`,
        error,
      );
      return null;
    }
  }

  protected safeExecute(operation: () => void, errorMessage: string): void {
    try {
      operation();
    } catch (error) {
      console.error(`[${this.constructor.name}] ${errorMessage}:`, error);
    }
  }
}
