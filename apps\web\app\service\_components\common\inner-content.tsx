"use client";

import { useAppQuery } from "@geon-query/react-query";
import { Search } from "lucide-react";
import { useEffect, useMemo, useState } from "react";

import { DynamicFilterManager } from "@/app/service/_components/dynamic-search/dynamic-filter-manager";
import DynamicResultTable, {
  ActionRow,
} from "@/app/service/_components/dynamic-search/dynamic-result-table";
import DynamicSearchForm from "@/app/service/_components/dynamic-search/dynamic-search-form";
import { useServiceSearchSchema } from "@/app/service/_components/hooks/use-service-search-schema";

import { useFacilitySearch } from "../../_contexts/facility-search";
import {
  type ServiceQueryResult,
  useServiceSearchClient,
} from "../hooks/use-service-search-client";
import { useServiceSearchHandle } from "../hooks/use-service-search-handle";
import { CustomHeader } from "./custom-header";
import { SchemaStateGuard } from "./schema-state-guard";
import { ServiceModal } from "./service-modal";

export function InnerContent() {
  const { selectedFacilities, selectedFacilityIds, selectedServiceId } =
    useFacilitySearch();

  // 검색 스키마 hooks 서비브용 스미마 관리용
  const {
    schema,
    schemaLoading,
    schemaError,
    availableFields,
    activeFieldsForUI,
    handleAddField,
    handleRemoveField,
  } = useServiceSearchSchema(selectedServiceId);

  // 검색 핸들 + 상태 hooks 검색용
  const {
    // 상태
    pageSize,
    setPageSize,
    pageNo,
    setPageNo,
    totalCount,
    setTotalCount,
    form,
    isSearching,
    hasSearched,
    // 모달 상태
    modalOpen,
    modalMode,
    selectedFacilityData,
    // 핸들러
    handleSearch,
    handleModalClose,
    handleRegistered,
    handleRowClick,
    handleAdminDistrictChange,
    handleResetDynamicSearch,
    handleAction,
  } = useServiceSearchHandle(selectedFacilityIds);

  // service client 서비스별 client API 호출 관리용 hooks
  const { client, config: serviceQueryConfig } = useServiceSearchClient(
    schema?.serviceId ?? selectedServiceId,
  );

  // 검색 결과 tableResult에 셋팅
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [facilityFilters, setFacilityFilters] = useState<string[]>([]);
  // 필터된 시설물들
  const filteredResults = useMemo(() => {
    if (facilityFilters.length === 0) return searchResults;
    return searchResults.filter((row) =>
      facilityFilters.includes(row._facilityId),
    );
  }, [searchResults, facilityFilters]);

  // data조회 useAppAuery 동적으로 변경
  const { data } = useAppQuery<ServiceQueryResult>({
    queryKey: serviceQueryConfig
      ? [
          serviceQueryConfig.key,
          schema?.serviceId ?? selectedServiceId,
          form,
          pageSize,
          pageNo,
        ]
      : ["service-search", schema?.serviceId ?? selectedServiceId],
    queryFn: async () => {
      if (!serviceQueryConfig) return { rows: [], totalCount: 0, raw: null };
      return serviceQueryConfig.fetcher({
        client,
        form,
        pageSize,
        pageNo,
        resource: schema?.serviceId ?? selectedServiceId,
      });
    },
    enabled: hasSearched && Boolean(serviceQueryConfig),
  });
  // 검색 row 선택
  const onAction = async (actionId: string, row: ActionRow) => {
    handleAction(actionId, row, schema!);
  };
  useEffect(() => {
    if (!data) return;
    setSearchResults(data.rows);
    setTotalCount(data.totalCount);
  }, [data, setTotalCount]);

  return (
    //예외 처리 가드
    <SchemaStateGuard
      schemaLoading={schemaLoading}
      schemaError={schemaError}
      schema={schema}
      selectedFacilities={selectedFacilities}
    >
      {(readySchema) => (
        <div className="flex h-full flex-col space-y-2 p-4">
          {/* 검색 폼 */}
          <div className="bg-card flex-shrink-0 space-y-4 rounded-lg border p-4">
            {/* 헤더 영역: 제목과 필터 관리 */}
            <div className="flex items-center justify-between">
              <h2 className="text-foreground text-sm font-medium">
                {readySchema.title}
              </h2>
              {/*검색 조건 */}
              <DynamicFilterManager
                activeFields={activeFieldsForUI}
                availableFields={availableFields}
                onAddField={handleAddField}
                onRemoveField={handleRemoveField}
                disabled={isSearching}
                compact
              />
            </div>

            {/* 검색 폼 */}
            <DynamicSearchForm
              schema={readySchema}
              onSubmit={handleSearch}
              compact
              loading={isSearching}
              collapsible={false}
              onAdminDistrictChange={handleAdminDistrictChange}
              onReset={handleResetDynamicSearch}
            />
          </div>
          {/*테이블 위에 헤더 */}
          <CustomHeader form={form} totalCount={totalCount} />
          {/* 검색 결과 */}
          <div className="min-h-0 flex-1">
            {!hasSearched ? (
              <div className="flex h-full items-center justify-center">
                <div className="text-center">
                  <Search className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                  <h3 className="mb-2 text-sm font-medium">
                    선택된 시설물:{" "}
                    {selectedFacilities.map((f) => f.title).join(", ")}
                  </h3>
                  <p className="text-muted-foreground mb-1 text-sm">
                    검색 조건을 입력하고 검색 버튼을 클릭하세요
                  </p>
                </div>
              </div>
            ) : (
              <div className="flex h-full flex-col">
                <div className="min-h-0 flex-1">
                  <DynamicResultTable
                    pageSize={pageSize}
                    onPageSizeChange={setPageSize}
                    onPageChange={setPageNo}
                    pageIndex={pageNo}
                    totalCount={totalCount}
                    columns={[
                      {
                        id: "actions",
                        label: "액션",
                        type: "actions",
                        actions: [
                          { id: "location", label: "위치" },
                          { id: "detail", label: "상세" },
                        ],
                      },
                      ...readySchema.result.columns,
                    ]}
                    rows={filteredResults}
                    onAction={onAction}
                    onRowClick={handleRowClick}
                    searchKey="name"
                    searchPlaceholder="검색..."
                    isLoading={isSearching}
                  />
                </div>
              </div>
            )}
          </div>

          {/* 시설물 상세/등록 모달 */}
          <ServiceModal
            serviceName={schema?.serviceId ?? "unknown"}
            isOpen={modalOpen}
            onClose={handleModalClose}
            mode={modalMode}
            facilityData={selectedFacilityData}
            facilityType={selectedFacilityData?.facilityType}
            onRegistered={handleRegistered}
          />
        </div>
      )}
    </SchemaStateGuard>
  );
}
