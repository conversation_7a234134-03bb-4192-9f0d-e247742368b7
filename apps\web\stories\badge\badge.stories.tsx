import { Badge } from "@geon-ui/react/primitives/badge";
import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import BadgeDemoSource from "!!raw-loader!./demo";

import { BadgeDemo } from "./demo";

const meta = {
  title: "Shadcn/Badge",
  component: Badge,
  tags: ["autodocs"],
  args: {},
  argTypes: {
    variant: {
      control: "inline-radio",
      options: ["default", "secondary", "outline", "destructive"],
      table: {
        type: {
          summary: "string",
          detail: `"default" | "secondary" | "outline" | "destructive"`,
        },
        defaultValue: { summary: "default" },
      },
    },
    children: { control: "text", table: { type: { summary: "ReactNode" } } },
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Badge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: "Default",
  },
};

/**
 * Shadcn Default Demo
 */
export const Demo: Story = {
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: BadgeDemoSource,
      },
    },
  },
  render: () => <BadgeDemo />,
};
