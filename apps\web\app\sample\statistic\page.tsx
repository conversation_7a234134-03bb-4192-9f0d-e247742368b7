"use client";
import { Map } from "@geon-map/react-odf";
import { Accordion } from "@geon-ui/react/primitives/accordion";
import React from "react";

import BasemapWidgetUse from "@/components/widget/basemap-widget-use";

import StatsPresetSetting from "./_components/statsPresetSetting";
import { StatsProvider } from "./_components/statsProvider";
import StatsRegist from "./_components/statsRegist";
import StatsStyleSetting from "./_components/statsStyleSetting";
import StatsTargetSetting from "./_components/statsTargetSetting";
import StatsValueSetting from "./_components/statsValueSetting";

export default function Page() {
  return (
    <React.Fragment>
      <Map className="h-screen w-full">
        <BasemapWidgetUse />

        <div className="bg-background absolute left-0 top-0 h-screen w-[400px] overflow-y-auto">
          {/* 통계지도 */}
          <StatsProvider>
            <Accordion
              type="single"
              collapsible
              className="w-full"
              defaultValue="item_step1"
            >
              <StatsTargetSetting />
              <StatsValueSetting />
              <StatsPresetSetting />
              <StatsStyleSetting />
            </Accordion>

            <StatsRegist />
          </StatsProvider>
        </div>
      </Map>
    </React.Fragment>
  );
}
